namespace OracleDB_MongoDB_Importer.Models;

public class CustomerConfig
{
    public string CustomerId { get; set; } = string.Empty;
    public string OracleConnectionStringTemplate { get; set; } = string.Empty;
    public string MongoDbConnectionStringTemplate { get; set; } = string.Empty;
    public string MongoDbDatabaseName { get; set; } = string.Empty;
    public bool IsEnabled { get; set; } = true;
}
