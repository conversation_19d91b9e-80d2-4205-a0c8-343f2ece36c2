# OracleDB MongoDB Importer - Benutzerdokumentation

## Überblick

Der OracleDB MongoDB Importer ist eine .NET-Anwendung, die automatisch Daten aus Oracle-Datenbanken in MongoDB-Collections importiert. Die Anwendung unterstützt verschiedene Synchronisationsstrategien und bietet ein Web-Dashboard zur Überwachung der Import-Jobs.

## Hauptfunktionen

- **Automatische Datenimporte** von Oracle-Views in MongoDB-Collections
- **Verschiedene Sync-Strategien**: Partial Import, Complete Replace, Initial Import
- **Zeitgesteuerte Jobs** mit konfigurierbaren Cron-Ausdrücken
- **Multi-Kunden-Support** mit individuellen Konfigurationen
- **Web-Dashboard** zur Überwachung und Verwaltung
- **REST-API** für manuelle Imports
- **Job-Monitoring** mit detaillierter Protokollierung

## Anwendung starten

### Voraussetzungen
- .NET 9.0 Runtime
- Zugriff auf Oracle-Datenbanken
- Zugriff auf MongoDB Atlas

### Lokaler Start
```bash
cd OracleDB_MongoDB_Importer
dotnet run
```

### Docker-Start
```bash
docker build -t oracle-mongo-importer .
docker run -p 8080:8080 oracle-mongo-importer
```

Die Anwendung läuft standardmäßig auf Port 8080.

## Dashboard öffnen

### Dashboard-URL
Nach dem Start der Anwendung ist das Dashboard unter folgender URL erreichbar:

```
http://localhost:8080/dashboard
```

### Anmeldedaten
- **Benutzername**: `admin`
- **Passwort**: `admin`

> **Hinweis**: Die Anmeldedaten können in der `appsettings.json` unter `TickerQBasicAuth` geändert werden.

### Dashboard-Funktionen

Das Dashboard bietet folgende Funktionen:

1. **Job-Übersicht**: Anzeige aller konfigurierten Jobs
2. **Job-Status**: Aktuelle Status der laufenden und abgeschlossenen Jobs
3. **Ausführungshistorie**: Verlauf der Job-Ausführungen
4. **Performance-Metriken**: Laufzeiten und verarbeitete Datensätze
5. **Fehlerprotokollierung**: Detaillierte Fehlermeldungen bei Problemen

## Konfiguration

### Kunden-Konfiguration

Kunden werden in der `appsettings.json` unter dem Abschnitt `Customers` konfiguriert:

```json
{
  "CustomerId": "storelogix_vertrieb2",
  "OracleConnectionStringTemplate": "User Id=VERTRIEB2;Password=*******;Data Source=...",
  "MongoDbConnectionStringTemplate": "mongodb+srv://cs-admin:{password}@...",
  "MongoDbDatabaseName": "storelogix_vertrieb2",
  "isEnabled": true
}
```

### Job-Konfiguration

Jobs werden unter `GlobalJobs` definiert:

```json
{
  "JobId": "V_EXP_KOMM_To_storelogix_komm",
  "OracleViewName": "V_EXP_KOMM",
  "MongoCollectionName": "storelogix_komm",
  "CronExpressionKey": "normaleTabellen",
  "SyncStrategy": "partialimport",
  "TimestampField": "LAST_UPDATE",
  "IdField": "ID",
  "InitialImportBatchSize": 5000,
  "SyncBatchSize": 500,
  "IsEnabled": true
}
```

### Zeitpläne

Cron-Ausdrücke werden unter `CronExpressions` definiert:

```json
{
  "normaleTabellen": "*/15 * * * *",    // Alle 15 Minuten
  "taeglicheTabellen": "0 6 * * *"      // Täglich um 6:00 Uhr
}
```

## Sync-Strategien

### 1. Partial Import (`partialimport`)
- Importiert nur neue oder geänderte Datensätze
- Basiert auf einem Timestamp-Feld
- Effizient für große Datenmengen
- Standard-Strategie für die meisten Jobs

### 2. Complete Replace (`completereplace`)
- Löscht die gesamte Collection und importiert alle Daten neu
- Verwendet für Daten ohne Timestamp-Feld
- Geeignet für kleinere Datenmengen oder Stammdaten

### 3. Initial Import (`initialimport`)
- Wird automatisch beim ersten Import verwendet
- Importiert alle verfügbaren Daten
- Wechselt danach automatisch zu Partial Import

## API-Endpunkte

### Health Check
```
GET /health
```
Überprüft den Status der Anwendung.

### Manueller Import aller Jobs
```
GET /api/ManualImport/initial-import-all
```
Startet einen Initial Import für alle aktivierten Kunden und Jobs.

**Antwort-Beispiel:**
```json
{
  "message": "Initial Import für alle Kunden und Jobs abgeschlossen",
  "totalJobs": 120,
  "successfulJobs": 118,
  "failedJobs": 2,
  "results": [...]
}
```

## Monitoring und Logging

### Job-Monitoring
- Aktivierung über Umgebungsvariable: `JOB_MONITORING=true`
- Speichert detaillierte Informationen über jeden Job-Lauf
- Verfügbar im Dashboard und über die MongoDB-Collection

### Logging
- Strukturierte Logs mit verschiedenen Log-Levels
- Konfiguration in `appsettings.json` unter `Logging`
- Logs enthalten Job-IDs, Kunden-IDs und Performance-Metriken

## Fehlerbehebung

### Häufige Probleme

1. **Dashboard nicht erreichbar**
   - Prüfen Sie, ob die Anwendung läuft
   - Überprüfen Sie die Port-Konfiguration
   - Kontrollieren Sie die Firewall-Einstellungen

2. **Anmeldung fehlgeschlagen**
   - Überprüfen Sie die Anmeldedaten in `appsettings.json`
   - Standard: admin/admin

3. **Jobs werden nicht ausgeführt**
   - Prüfen Sie die `IsEnabled`-Flags in der Konfiguration
   - Überprüfen Sie die Cron-Ausdrücke
   - Kontrollieren Sie die Datenbankverbindungen

4. **Verbindungsfehler**
   - Überprüfen Sie die Oracle-Verbindungsstrings
   - Testen Sie die MongoDB-Verbindung
   - Prüfen Sie Netzwerk-Konnektivität

### Log-Analyse
Wichtige Log-Nachrichten:
- `Job {JobId} erfolgreich abgeschlossen`: Erfolgreicher Job-Abschluss
- `Fehler beim Ausführen von Job {JobId}`: Job-Fehler
- `MongoDB Verbindung fehlgeschlagen`: Datenbankverbindungsproblem

## Wartung

### Regelmäßige Aufgaben
1. Überwachung der Job-Ausführungen im Dashboard
2. Kontrolle der Log-Dateien auf Fehler
3. Überprüfung der Datenbankverbindungen
4. Performance-Monitoring der Import-Zeiten

### Konfigurationsänderungen
- Änderungen in `appsettings.json` erfordern einen Neustart
- Neue Kunden oder Jobs können zur Laufzeit hinzugefügt werden
- Cron-Ausdrücke werden bei der nächsten Ausführung übernommen

## Support

Bei Problemen oder Fragen:
1. Überprüfen Sie die Logs im Dashboard
2. Kontrollieren Sie die Konfigurationsdateien
3. Testen Sie die Datenbankverbindungen
4. Konsultieren Sie diese Dokumentation
