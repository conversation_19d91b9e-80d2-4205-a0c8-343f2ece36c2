using MongoDB.Driver;
using OracleDB_MongoDB_Importer.Models;

namespace OracleDB_MongoDB_Importer.Repositories;

public class MonitoringRepository : IMonitoringRepository
{
    private readonly IMongoCollection<JobMonitoringLog> _collection;

    public MonitoringRepository(IConfiguration configuration)
    {
        var connectionString = configuration.GetConnectionString("MongoDB") ??
            "mongodb+srv://cs-admin:{password}@cs1.lwgp4.mongodb.net/?connectTimeoutMS=10000&maxIdleTimeMS=30000&socketTimeoutMS=5000";
        connectionString = connectionString.Replace("{password}", Environment.GetEnvironmentVariable("MONGODB_PASSWORD") ?? string.Empty);
        var client = new MongoClient(connectionString);
        var database = client.GetDatabase("storelogix_logs");
        _collection = database.GetCollection<JobMonitoringLog>("job_monitoring_logs");
    }

    public async Task SaveJobMonitoringLogAsync(JobMonitoringLog log, CancellationToken cancellationToken = default)
    {
        await _collection.InsertOneAsync(log, cancellationToken: cancellationToken);
    }

    public async Task UpdateJobMonitoringLogAsync(JobMonitoringLog log, CancellationToken cancellationToken = default)
    {
        var filter = Builders<JobMonitoringLog>.Filter.Eq(x => x.Id, log.Id);
        await _collection.ReplaceOneAsync(filter, log, cancellationToken: cancellationToken);
    }

    public async Task<JobMonitoringLog?> GetJobMonitoringLogAsync(string jobId, CancellationToken cancellationToken = default)
    {
        var filter = Builders<JobMonitoringLog>.Filter.Eq(x => x.JobId, jobId);
        return await _collection.Find(filter)
            .SortByDescending(x => x.StartTime)
            .FirstOrDefaultAsync(cancellationToken);
    }

    public async Task<bool> TestConnectionAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            await _collection.Find(new FilterDefinitionBuilder<JobMonitoringLog>().Empty).FirstOrDefaultAsync(cancellationToken);
            return true;
        }
        catch
        {
            return false;
        }
    }
}
