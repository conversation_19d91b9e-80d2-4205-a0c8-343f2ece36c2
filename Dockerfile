FROM mcr.microsoft.com/dotnet/sdk:9.0 AS build
WORKDIR /src
COPY OracleDB_MongoDB_Importer/OracleDB_MongoDB_Importer.csproj OracleDB_MongoDB_Importer/
COPY OracleDB_MongoDB_Importer.sln ./
RUN dotnet restore OracleDB_MongoDB_Importer/OracleDB_MongoDB_Importer.csproj
COPY . .
WORKDIR /src/OracleDB_MongoDB_Importer
RUN dotnet build OracleDB_MongoDB_Importer.csproj -c Release -o /app/build
RUN dotnet publish OracleDB_MongoDB_Importer.csproj -c Release -o /app/publish /p:UseAppHost=false

FROM mcr.microsoft.com/dotnet/aspnet:9.0 AS final
RUN apt-get update && apt-get install -y curl && rm -rf /var/lib/apt/lists/*
WORKDIR /app
COPY --from=build /app/publish .
RUN adduser --disabled-password --gecos '' --shell /bin/bash --uid 1001 appuser
RUN chown -R appuser:appuser /app
USER appuser
EXPOSE 8080
ENV ASPNETCORE_ENVIRONMENT=Production
ENV ASPNETCORE_URLS=http://+:8080
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 CMD sh -c 'code=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:8080/health) && [ "$code" = "200" ] || exit 1'
ENTRYPOINT ["dotnet", "OracleDB_MongoDB_Importer.dll"]
