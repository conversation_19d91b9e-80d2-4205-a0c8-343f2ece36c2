using OracleDB_MongoDB_Importer.Repositories;

namespace OracleDB_MongoDB_Importer.Services;

public class ConnectionTestService : IConnectionTestService
{
    private readonly IOracleRepository _oracleRepository;
    private readonly IMongoRepository _mongoRepository;
    private readonly ILogger<ConnectionTestService> _logger;

    public ConnectionTestService(
        IOracleRepository oracleRepository,
        IMongoRepository mongoRepository,
        ILogger<ConnectionTestService> logger)
    {
        _oracleRepository = oracleRepository;
        _mongoRepository = mongoRepository;
        _logger = logger;
    }

    public async Task<bool> TestOracleConnectionAsync(string customerId)
    {
        _logger.LogInformation("Teste Oracle-Verbindung für Kunde {CustomerId}", customerId);
        var result = await _oracleRepository.TestConnectionAsync(customerId);

        if (result)
        {
            _logger.LogInformation("Oracle-Verbindung für Kunde {CustomerId} erfolgreich", customerId);
        }
        else
        {
            _logger.LogWarning("Oracle-Verbindung für Kunde {CustomerId} fehlgeschlagen", customerId);
        }

        return result;
    }

    public async Task<bool> TestMongoDbConnectionAsync(string customerId)
    {
        _logger.LogInformation("Teste MongoDB-Verbindung für Kunde {CustomerId}", customerId);
        var result = await _mongoRepository.TestConnectionAsync(customerId);

        if (result)
        {
            _logger.LogInformation("MongoDB-Verbindung für Kunde {CustomerId} erfolgreich", customerId);
        }
        else
        {
            _logger.LogWarning("MongoDB-Verbindung für Kunde {CustomerId} fehlgeschlagen", customerId);
        }

        return result;
    }

    public async Task<(bool Oracle, bool MongoDB)> TestAllConnectionsAsync(string customerId)
    {
        _logger.LogInformation("Teste alle Verbindungen für Kunde {CustomerId}", customerId);

        var oracleTask = TestOracleConnectionAsync(customerId);
        var mongoTask = TestMongoDbConnectionAsync(customerId);

        await Task.WhenAll(oracleTask, mongoTask);

        var oracleResult = await oracleTask;
        var mongoResult = await mongoTask;

        _logger.LogInformation("Verbindungstests für Kunde {CustomerId} abgeschlossen: Oracle={Oracle}, MongoDB={MongoDB}",
            customerId, oracleResult, mongoResult);

        return (oracleResult, mongoResult);
    }
}
