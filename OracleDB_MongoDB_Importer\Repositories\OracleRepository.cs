using Oracle.ManagedDataAccess.Client;
using OracleDB_MongoDB_Importer.Factories;
using OracleDB_MongoDB_Importer.Services;
using System.Data;
using MongoDB.Bson;
using System.Runtime.CompilerServices;

namespace OracleDB_MongoDB_Importer.Repositories;

public class OracleRepository : IOracleRepository
{
    private readonly IOracleConnectionFactory _connectionFactory;
    private readonly ILogger<OracleRepository> _logger;
    private readonly IOracleTypeConversionService _typeConversionService;

    public OracleRepository(IOracleConnectionFactory connectionFactory, ILogger<OracleRepository> logger, IOracleTypeConversionService typeConversionService)
    {
        _connectionFactory = connectionFactory;
        _logger = logger;
        _typeConversionService = typeConversionService;
    }

    public async Task<bool> TestConnectionAsync(string customerId)
    {
        try
        {
            using var connection = await GetConnectionAsync(customerId);
            await connection.OpenAsync();
            return connection.State == System.Data.ConnectionState.Open;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Fehler beim Testen der Oracle-Verbindung für Kunde {CustomerId}", customerId);
            return false;
        }
    }

    public Task<OracleConnection> GetConnectionAsync(string customerId)
    {
        var connection = _connectionFactory.CreateConnection(customerId);
        return Task.FromResult(connection);
    }

    public async Task<DataTable> GetDataFromViewAsync(string customerId, string viewName, int batchSize = 1000, int offset = 0)
    {
        using var connection = await GetConnectionAsync(customerId);
        await connection.OpenAsync();

        var query = $"SELECT * FROM {viewName}";

        using var command = new OracleCommand(query, connection);
        command.FetchSize = Math.Min(batchSize * 1024, 256 * 1024);

        using var reader = await command.ExecuteReaderAsync();

        var dataTable = new DataTable();

        if (reader.HasRows)
        {
            for (int i = 0; i < reader.FieldCount; i++)
            {
                dataTable.Columns.Add(reader.GetName(i), reader.GetFieldType(i));
            }

            int currentRow = 0;
            while (await reader.ReadAsync())
            {
                if (currentRow < offset)
                {
                    currentRow++;
                    continue;
                }

                if (dataTable.Rows.Count >= batchSize)
                    break;

                var row = dataTable.NewRow();
                for (int i = 0; i < reader.FieldCount; i++)
                {
                    row[i] = reader.IsDBNull(i) ? DBNull.Value : _typeConversionService.ConvertToSafeObject(reader, i);
                }
                dataTable.Rows.Add(row);
                currentRow++;
            }
        }

        return dataTable;
    }

    public async Task<int> GetTotalRecordCountAsync(string customerId, string viewName)
    {
        using var connection = await GetConnectionAsync(customerId);
        await connection.OpenAsync();

        var query = $"SELECT COUNT(*) FROM {viewName}";

        using var command = new OracleCommand(query, connection);
        var result = await command.ExecuteScalarAsync();

        var count = Convert.ToInt32(result);

        return count;
    }

    public async IAsyncEnumerable<BsonDocument> GetDataStreamAsync(string customerId, string viewName, [EnumeratorCancellation] CancellationToken cancellationToken = default)
    {
        using var connection = await GetConnectionAsync(customerId);
        await connection.OpenAsync(cancellationToken);

        var query = $"SELECT * FROM {viewName}";
        using var command = new OracleCommand(query, connection);
        command.FetchSize = 256 * 1024;

        using var reader = await command.ExecuteReaderAsync(cancellationToken);

        while (await reader.ReadAsync(cancellationToken))
        {
            cancellationToken.ThrowIfCancellationRequested();

            var document = new BsonDocument();
                            for (int i = 0; i < reader.FieldCount; i++)
                {
                    var fieldName = reader.GetName(i);
                    var value = reader.IsDBNull(i) ? BsonNull.Value : _typeConversionService.ConvertToSafeBsonValue(reader, i);
                    document[fieldName] = value;
                }

            yield return document;
        }

    }

    public async Task<List<object>> GetModifiedIdsSinceAsync(string viewName, string idField, string timestampField, string customerId, DateTime? lastTimestamp, CancellationToken cancellationToken = default)
    {
        await using var connection = await GetConnectionAsync(customerId);
        await connection.OpenAsync(cancellationToken);

        if (lastTimestamp == null)
        {
            return new List<object>();
        }

        var adjustedTimestamp = lastTimestamp.Value;
        var ids = new List<object>();
        var sql = $"SELECT \"{idField}\" FROM \"{viewName}\" WHERE \"{timestampField}\" >= :lastTimestamp";

        await using var command = new OracleCommand(sql, connection);

        command.Parameters.Add(new OracleParameter("lastTimestamp", OracleDbType.TimeStampTZ, adjustedTimestamp, ParameterDirection.Input));

        try
        {

            await using var reader = await command.ExecuteReaderAsync(CommandBehavior.SingleResult, cancellationToken);

            while (await reader.ReadAsync(cancellationToken))
            {
                ids.Add(reader.GetValue(0));
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting modified IDs from Oracle view {ViewName}. SQL: {SQL}", viewName, sql);
            throw;
        }

        return ids;
    }

    public async IAsyncEnumerable<BsonDocument> GetDataByIdsStreamAsync(string customerId, string viewName, string idField,
        IEnumerable<object> ids, [EnumeratorCancellation] CancellationToken cancellationToken = default)
    {
        var idList = ids.ToList();
        if (!idList.Any())
        {
            yield break;
        }

        using var connection = await GetConnectionAsync(customerId);
        await connection.OpenAsync(cancellationToken);

        const int batchSize = 1000;
        var totalProcessed = 0;

        for (int i = 0; i < idList.Count; i += batchSize)
        {
            var batch = idList.Skip(i).Take(batchSize).ToList();
            var parameters = string.Join(",", batch.Select((_, index) => $":id{index}"));
            var query = $"SELECT * FROM {viewName} WHERE {idField} IN ({parameters})";

            using var command = new OracleCommand(query, connection);

            for (int j = 0; j < batch.Count; j++)
            {
                command.Parameters.Add(new OracleParameter($"id{j}", batch[j]));
            }

            command.FetchSize = 256 * 1024;

            using var reader = await command.ExecuteReaderAsync(cancellationToken);

            while (await reader.ReadAsync(cancellationToken))
            {
                cancellationToken.ThrowIfCancellationRequested();

                var document = new BsonDocument();
                for (int k = 0; k < reader.FieldCount; k++)
                {
                    var fieldName = reader.GetName(k);
                    var value = reader.IsDBNull(k) ? BsonNull.Value : _typeConversionService.ConvertToSafeBsonValue(reader, k);
                    document[fieldName] = value;
                }

                yield return document;
                totalProcessed++;
            }

            command.Parameters.Clear();
        }

    }









}
