{"MongoDBSettings": {"WeakServerOptimization": {"Enabled": true, "ConnectionSettings": {"ConnectTimeoutSeconds": 60, "SocketTimeoutSeconds": 120, "ServerSelectionTimeoutSeconds": 30, "HeartbeatTimeoutSeconds": 30, "HeartbeatIntervalSeconds": 20}, "ConnectionPoolSettings": {"MaxPoolSize": 50, "MinPoolSize": 5, "MaxConnectionIdleTimeMinutes": 30, "MaxConnectionLifeTimeMinutes": 0, "WaitQueueTimeoutMinutes": 5, "MaxConnecting": 5}, "RetrySettings": {"MaxRetries": 3, "ExponentialBackoffBase": 2, "RetryWrites": true, "RetryReads": true}, "PerformanceSettings": {"BatchInsertDelayMs": 2000, "UseUnorderedInserts": true, "GarbageCollectAfterBatches": 10, "ReadPreference": "SecondaryPreferred"}}}}