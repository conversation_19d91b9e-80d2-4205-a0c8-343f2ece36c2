using OracleDB_MongoDB_Importer;
using OracleDB_MongoDB_Importer.Factories;
using OracleDB_MongoDB_Importer.Repositories;
using OracleDB_MongoDB_Importer.Services;
using OracleDB_MongoDB_Importer.Strategies;
using TickerQ.Dashboard.DependencyInjection;
using TickerQ.DependencyInjection;

var builder = WebApplication.CreateBuilder(args);

builder.Services.AddSingleton<IOracleConnectionFactory, OracleConnectionFactory>();
builder.Services.AddTransient<IOracleTypeConversionService, OracleTypeConversionService>();
builder.Services.AddTransient<IOracleRepository, OracleRepository>();
builder.Services.AddSingleton<IMongoRepository, MongoRepository>();
builder.Services.AddSingleton<IMonitoringRepository, MonitoringRepository>();
builder.Services.AddTransient<IConnectionTestService, ConnectionTestService>();
builder.Services.AddTransient<IImportService, ImportService>();
builder.Services.AddSingleton<JobMonitoringService>();

builder.Services.AddScoped<CompleteReplaceStrategy>();
builder.Services.AddScoped<InitialImportStrategy>();
builder.Services.AddScoped<PartialImport>();
builder.Services.AddScoped<IImportStrategyFactory, ImportStrategyFactory>();

builder.Services.AddScoped<GlobalJobTickerService>();

builder.Services.AddControllers();

// builder.Services.AddHostedService<Worker>();
builder.Services.AddTickerQ(opt =>
{
    // Set your class that implements ITickerExceptionHandler.
    // opt.SetExceptionHandler<MyExceptionHandlerClass>();
    // Set the max thread concurrency for Ticker (default: Environment.ProcessorCount).
    // opt.SetMaxConcurrency(maxConcurrency: 2);
    opt.AddDashboard(basePath: "/dashboard");
    opt.AddDashboardBasicAuth();
});

var app = builder.Build();

app.MapControllers();
((IApplicationBuilder)app).UseTickerQ();

app.MapGet("/health", () => Results.Ok("OK"));



app.Run();
