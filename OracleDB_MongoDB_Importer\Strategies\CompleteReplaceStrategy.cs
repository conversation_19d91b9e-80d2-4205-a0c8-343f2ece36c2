using OracleDB_MongoDB_Importer.Models;
using OracleDB_MongoDB_Importer.Repositories;

namespace OracleDB_MongoDB_Importer.Strategies;

public class CompleteReplaceStrategy : IImportStrategy
{
    private readonly IOracleRepository _oracleRepository;
    private readonly IMongoRepository _mongoRepository;
    private readonly ILogger<CompleteReplaceStrategy> _logger;

    public CompleteReplaceStrategy(
        IOracleRepository oracleRepository,
        IMongoRepository mongoRepository,
        ILogger<CompleteReplaceStrategy> logger)
    {
        _oracleRepository = oracleRepository;
        _mongoRepository = mongoRepository;
        _logger = logger;
    }

    public async Task<long> ExecuteAsync(string customerId, JobConfig jobConfig, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Starte CompleteReplaceStrategy für Job {JobId}, View: {ViewName}, Collection: {CollectionName}",
            jobConfig.JobId, jobConfig.OracleViewName, jobConfig.MongoCollectionName);

        try
        {
            await _mongoRepository.DeleteCollectionAsync(customerId, jobConfig.MongoCollectionName);

            var documentStream = _oracleRepository.GetDataStreamAsync(customerId, jobConfig.OracleViewName, cancellationToken);

            await _mongoRepository.InsertDocumentStreamAsync(
                customerId,
                jobConfig.MongoCollectionName,
                documentStream,
                jobConfig.InitialImportBatchSize,
                cancellationToken);

            var finalCount = await _mongoRepository.GetDocumentCountAsync(customerId, jobConfig.MongoCollectionName);
            _logger.LogInformation("CompleteReplace-Import erfolgreich abgeschlossen. {FinalCount} Dokumente in Collection {CollectionName}",
                finalCount, jobConfig.MongoCollectionName);

            return finalCount;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Fehler beim Stream-basierten CompleteReplace-Import für Job {JobId}", jobConfig.JobId);
            throw;
        }
    }
}
