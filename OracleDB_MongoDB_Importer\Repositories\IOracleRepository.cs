using Oracle.ManagedDataAccess.Client;
using System.Data;
using MongoDB.Bson;

namespace OracleDB_MongoDB_Importer.Repositories;

public interface IOracleRepository
{
    Task<bool> TestConnectionAsync(string customerId);
    Task<OracleConnection> GetConnectionAsync(string customerId);
    Task<DataTable> GetDataFromViewAsync(string customerId, string viewName, int batchSize = 1000, int offset = 0);
    IAsyncEnumerable<BsonDocument> GetDataStreamAsync(string customerId, string viewName, CancellationToken cancellationToken = default);
    Task<int> GetTotalRecordCountAsync(string customerId, string viewName);

    public Task<List<object>> GetModifiedIdsSinceAsync(string viewName, string idField, string timestampField,
        string customerId, DateTime? lastTimestamp, CancellationToken cancellationToken = default);

    IAsyncEnumerable<BsonDocument> GetDataByIdsStreamAsync(string customerId, string viewName, string idField,
        IEnumerable<object> ids, CancellationToken cancellationToken = default);

}
