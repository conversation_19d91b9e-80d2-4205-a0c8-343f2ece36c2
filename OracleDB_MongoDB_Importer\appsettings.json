{"ConnectionStrings": {"MongoDB": "mongodb+srv://cs-admin:{password}@cs1.lwgp4.mongodb.net/?connectTimeoutMS=60000&maxIdleTimeMS=120000&socketTimeoutMS=120000&serverSelectionTimeoutMS=30000&heartbeatFrequencyMS=20000&maxPoolSize=50&minPoolSize=5&maxConnecting=5&waitQueueTimeoutMS=300000"}, "TickerQBasicAuth": {"Username": "admin", "Password": "admin"}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft.Hosting.Lifetime": "Information"}}, "CronExpressions": {"normaleTabellen": "*/15 * * * *", "taeglicheTabellen": "0 6 * * *"}, "Customers": [{"CustomerId": "storelogix_sendsally", "OracleConnectionStringTemplate": "User Id=SENDSALLY;Password={password};Data Source=csoraprod2.rzhit.win:1521/slprod2.rzhit.win", "MongoDbConnectionStringTemplate": "mongodb+srv://cs-admin:{password}@cs1.lwgp4.mongodb.net/?connectTimeoutMS=60000&maxIdleTimeMS=120000&socketTimeoutMS=120000&serverSelectionTimeoutMS=30000&heartbeatFrequencyMS=20000&maxPoolSize=50&minPoolSize=5&maxConnecting=5&waitQueueTimeoutMS=300000", "MongoDbDatabaseName": "storelogix_sendsally", "isEnabled": true}, {"CustomerId": "storelogix_vertrieb2", "OracleConnectionStringTemplate": "User Id=VERTRIEB2;Password=*******;Data Source=csorastby1.rzhit.win:1521/sltest.rzhit.win", "MongoDbConnectionStringTemplate": "mongodb+srv://cs-admin:{password}@cs1.lwgp4.mongodb.net/?connectTimeoutMS=60000&maxIdleTimeMS=120000&socketTimeoutMS=120000&serverSelectionTimeoutMS=30000&heartbeatFrequencyMS=20000&maxPoolSize=50&minPoolSize=5&maxConnecting=5&waitQueueTimeoutMS=300000", "MongoDbDatabaseName": "storelogix_vertrieb2", "isEnabled": true}, {"CustomerId": "storelogix_cella", "OracleConnectionStringTemplate": "User Id=EUROTIME;Password={password};Data Source=csoraprod2.rzhit.win:1521/slprod2.rzhit.win", "MongoDbConnectionStringTemplate": "mongodb+srv://cs-admin:{password}@cs1.lwgp4.mongodb.net/?connectTimeoutMS=60000&maxIdleTimeMS=120000&socketTimeoutMS=120000&serverSelectionTimeoutMS=30000&heartbeatFrequencyMS=20000&maxPoolSize=50&minPoolSize=5&maxConnecting=5&waitQueueTimeoutMS=300000", "MongoDbDatabaseName": "storelogix_cella", "isEnabled": true}, {"CustomerId": "storelogix_kaufland", "OracleConnectionStringTemplate": "User Id=REAL;Password={password};Data Source=csoraprod2.rzhit.win:1521/slprod2.rzhit.win", "MongoDbConnectionStringTemplate": "mongodb+srv://cs-admin:{password}@cs1.lwgp4.mongodb.net/?connectTimeoutMS=60000&maxIdleTimeMS=120000&socketTimeoutMS=120000&serverSelectionTimeoutMS=30000&heartbeatFrequencyMS=20000&maxPoolSize=50&minPoolSize=5&maxConnecting=5&waitQueueTimeoutMS=300000", "MongoDbDatabaseName": "storelogix_kaufland", "isEnabled": true}, {"CustomerId": "storelogix_transco", "OracleConnectionStringTemplate": "User Id=TRANSCO;Password={password};Data Source=csoraprod2.rzhit.win:1521/slprod2.rzhit.win", "MongoDbConnectionStringTemplate": "mongodb+srv://cs-admin:{password}@cs1.lwgp4.mongodb.net/?connectTimeoutMS=60000&maxIdleTimeMS=120000&socketTimeoutMS=120000&serverSelectionTimeoutMS=30000&heartbeatFrequencyMS=20000&maxPoolSize=50&minPoolSize=5&maxConnecting=5&waitQueueTimeoutMS=300000", "MongoDbDatabaseName": "storelogix_transco", "isEnabled": true}, {"CustomerId": "storelogix_ffs", "OracleConnectionStringTemplate": "User Id=FFS;Password={password};Data Source=csoraprod1.rzhit.win:1521/slprod1.rzhit.win", "MongoDbConnectionStringTemplate": "mongodb+srv://cs-admin:{password}@cs1.lwgp4.mongodb.net/?connectTimeoutMS=60000&maxIdleTimeMS=120000&socketTimeoutMS=120000&serverSelectionTimeoutMS=30000&heartbeatFrequencyMS=20000&maxPoolSize=50&minPoolSize=5&maxConnecting=5&waitQueueTimeoutMS=300000", "MongoDbDatabaseName": "storelogix_ffs", "isEnabled": true}, {"CustomerId": "storelogix_zen", "OracleConnectionStringTemplate": "User Id=ZEN;Password={password};Data Source=csoraprod2.rzhit.win:1521/slprod2.rzhit.win", "MongoDbConnectionStringTemplate": "mongodb+srv://cs-admin:{password}@cs1.lwgp4.mongodb.net/?connectTimeoutMS=60000&maxIdleTimeMS=120000&socketTimeoutMS=120000&serverSelectionTimeoutMS=30000&heartbeatFrequencyMS=20000&maxPoolSize=50&minPoolSize=5&maxConnecting=5&waitQueueTimeoutMS=300000", "MongoDbDatabaseName": "storelogix_zen", "isEnabled": true}, {"CustomerId": "storelogix_dfs", "OracleConnectionStringTemplate": "User Id=DFS;Password={password};Data Source=csoraprod1.rzhit.win:1521/slprod1.rzhit.win", "MongoDbConnectionStringTemplate": "mongodb+srv://cs-admin:{password}@cs1.lwgp4.mongodb.net/?connectTimeoutMS=60000&maxIdleTimeMS=120000&socketTimeoutMS=120000&serverSelectionTimeoutMS=30000&heartbeatFrequencyMS=20000&maxPoolSize=50&minPoolSize=5&maxConnecting=5&waitQueueTimeoutMS=300000", "MongoDbDatabaseName": "storelogix_dfs", "isEnabled": true}, {"CustomerId": "storelogix_schroeder", "OracleConnectionStringTemplate": "User Id=SCHROEDER;Password={password};Data Source=csoraprod2.rzhit.win:1521/slprod2.rzhit.win", "MongoDbConnectionStringTemplate": "mongodb+srv://cs-admin:{password}@cs1.lwgp4.mongodb.net/?connectTimeoutMS=60000&maxIdleTimeMS=120000&socketTimeoutMS=120000&serverSelectionTimeoutMS=30000&heartbeatFrequencyMS=20000&maxPoolSize=50&minPoolSize=5&maxConnecting=5&waitQueueTimeoutMS=300000", "MongoDbDatabaseName": "storelogix_schroeder", "isEnabled": true}, {"CustomerId": "storelogix_petromax", "OracleConnectionStringTemplate": "User Id=PETROMAX;Password={password};Data Source=csoraprod1.rzhit.win:1521/slprod1.rzhit.win", "MongoDbConnectionStringTemplate": "mongodb+srv://cs-admin:{password}@cs1.lwgp4.mongodb.net/?connectTimeoutMS=60000&maxIdleTimeMS=120000&socketTimeoutMS=120000&serverSelectionTimeoutMS=30000&heartbeatFrequencyMS=20000&maxPoolSize=50&minPoolSize=5&maxConnecting=5&waitQueueTimeoutMS=300000", "MongoDbDatabaseName": "storelogix_petromax", "isEnabled": true}], "GlobalJobs": [{"JobId": "V_EXP_KOMM_To_storelogix_komm", "OracleViewName": "V_EXP_KOMM", "MongoCollectionName": "storelogix_komm", "CronExpressionKey": "normaleTabellen", "SyncStrategy": "partialimport", "TimestampField": "LAST_UPDATE", "IdField": "ID", "InitialImportBatchSize": 5000, "SyncBatchSize": 500, "IsEnabled": true}, {"JobId": "V_EXP_BESTAND_To_storelogix_stock", "OracleViewName": "V_EXP_BESTAND", "MongoCollectionName": "storelogix_stock", "CronExpressionKey": "taeg<PERSON><PERSON>abellen", "SyncStrategy": "partialimport", "TimestampField": "LAST_UPDATE", "IdField": "ID", "InitialImportBatchSize": 5000, "SyncBatchSize": 5000, "IsEnabled": true}, {"JobId": "V_EXP_WARENEINGANG_To_storelogix_we", "OracleViewName": "V_EXP_WARENEINGANG", "MongoCollectionName": "storelogix_we", "CronExpressionKey": "normaleTabellen", "SyncStrategy": "partialimport", "TimestampField": "LAST_UPDATE", "IdField": "ID", "InitialImportBatchSize": 5000, "SyncBatchSize": 500, "IsEnabled": true}, {"JobId": "V_EXP_VERLADUNG_To_storelogix_verl", "OracleViewName": "V_EXP_VERLADUNG", "MongoCollectionName": "storelogix_verl", "CronExpressionKey": "normaleTabellen", "SyncStrategy": "partialimport", "TimestampField": "LAST_UPDATE", "IdField": "ID", "InitialImportBatchSize": 5000, "SyncBatchSize": 500, "IsEnabled": true}, {"JobId": "V_EXP_RETOUREN_To_storelogix_returns", "OracleViewName": "V_EXP_RETOUREN", "MongoCollectionName": "storelogix_returns", "CronExpressionKey": "normaleTabellen", "SyncStrategy": "partialimport", "TimestampField": "LAST_UPDATE", "IdField": "ID", "InitialImportBatchSize": 5000, "SyncBatchSize": 500, "IsEnabled": true}, {"JobId": "V_EXP_AUFTRAG_To_storelogix_orders", "OracleViewName": "V_EXP_AUFTRAG", "MongoCollectionName": "storelogix_orders", "CronExpressionKey": "normaleTabellen", "SyncStrategy": "partialimport", "TimestampField": "LAST_UPDATE", "IdField": "ID", "InitialImportBatchSize": 5000, "SyncBatchSize": 500, "IsEnabled": true}, {"JobId": "V_EXP_AUFTRAG_VERSENDET_To_storelogix_orders_sent", "OracleViewName": "V_EXP_AUFTRAG_VERSENDET", "MongoCollectionName": "storelogix_orders_sent", "CronExpressionKey": "normaleTabellen", "SyncStrategy": "partialimport", "TimestampField": "LAST_UPDATE", "IdField": "ID", "InitialImportBatchSize": 5000, "SyncBatchSize": 500, "IsEnabled": true}, {"JobId": "V_EXP_BESTAND_MOVES_To_storelogix_stock_moves", "OracleViewName": "V_EXP_BESTAND_MOVES", "MongoCollectionName": "storelogix_stock_moves", "CronExpressionKey": "normaleTabellen", "SyncStrategy": "partialimport", "TimestampField": null, "IdField": null, "InitialImportBatchSize": 5000, "SyncBatchSize": 5000, "IsEnabled": true}, {"JobId": "V_EXP_TA_To_storelogix_ta", "OracleViewName": "V_EXP_TA", "MongoCollectionName": "storelogix_ta", "CronExpressionKey": "normaleTabellen", "SyncStrategy": "partialimport", "TimestampField": "LAST_UPDATE", "IdField": "ID", "InitialImportBatchSize": 5000, "SyncBatchSize": 500, "IsEnabled": true}, {"JobId": "V_EXP_SESSION_NOW_To_storelogix_session_now", "OracleViewName": "V_EXP_SESSION_NOW", "MongoCollectionName": "storelogix_session_now", "CronExpressionKey": "normaleTabellen", "SyncStrategy": "completereplace", "TimestampField": null, "IdField": null, "InitialImportBatchSize": 5000, "SyncBatchSize": 5000, "IsEnabled": true}, {"JobId": "V_EXP_BESTAND_NOW_To_storelogix_stock_now", "OracleViewName": "V_EXP_BESTAND_NOW", "MongoCollectionName": "storelogix_stock_now", "CronExpressionKey": "normaleTabellen", "SyncStrategy": "partialimport", "TimestampField": "LAST_UPDATE", "IdField": "ID", "InitialImportBatchSize": 5000, "SyncBatchSize": 500, "IsEnabled": true}]}