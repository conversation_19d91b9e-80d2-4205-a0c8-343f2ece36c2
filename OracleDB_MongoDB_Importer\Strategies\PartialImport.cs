﻿using OracleDB_MongoDB_Importer.Models;
using OracleDB_MongoDB_Importer.Repositories;

namespace OracleDB_MongoDB_Importer.Strategies;

public class PartialImport : IImportStrategy
{
    private readonly IOracleRepository _oracleRepository;
    private readonly IMongoRepository _mongoRepository;
    private readonly ILogger<PartialImport> _logger;

    public PartialImport(
        IOracleRepository oracleRepository,
        IMongoRepository mongoRepository,
        ILogger<PartialImport> logger)
    {
        _oracleRepository = oracleRepository;
        _mongoRepository = mongoRepository;
        _logger = logger;
    }

    public async Task<long> ExecuteAsync(string customerId, JobConfig jobConfig, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Starte PartialImport für Job {JobId}, View: {ViewName}, Collection: {CollectionName}",
            jobConfig.JobId, jobConfig.OracleViewName, jobConfig.MongoCollectionName);

        try
        {

            await _mongoRepository.EnsureIndexesForPartialImportAsync(
                customerId,
                jobConfig.MongoCollectionName,
                jobConfig.TimestampField ?? string.Empty,
                jobConfig.IdField,
                cancellationToken);

            var lastTimestamp = await _mongoRepository.GetLastUpdateTimestampAsync(
                customerId, jobConfig.MongoCollectionName, jobConfig.TimestampField ?? string.Empty, cancellationToken);

            if (lastTimestamp == null)
            {
                throw new InvalidOperationException("PartialImport benötigt einen vorhandenen Timestamp");
            }

            var ids = await _oracleRepository.GetModifiedIdsSinceAsync(jobConfig.OracleViewName, jobConfig.IdField ?? string.Empty,
                jobConfig.TimestampField ?? string.Empty, customerId, lastTimestamp, cancellationToken);

            if (!ids.Any())
            {
                _logger.LogInformation("Keine geänderten Datensätze gefunden seit {LastUpdate}", lastTimestamp.Value);
                return 0;
            }

            await _mongoRepository.DeleteDocumentsByIdsAsync(customerId, jobConfig.MongoCollectionName,
                jobConfig.IdField ?? string.Empty, ids, cancellationToken);

            var dataStream = _oracleRepository.GetDataByIdsStreamAsync(customerId, jobConfig.OracleViewName,
                jobConfig.IdField ?? string.Empty, ids, cancellationToken);

            await _mongoRepository.InsertDocumentStreamAsync(customerId, jobConfig.MongoCollectionName,
                dataStream, 1000, cancellationToken);

            _logger.LogInformation("PartialImport für Job {JobId} erfolgreich abgeschlossen. {Count} Datensätze aktualisiert",
                jobConfig.JobId, ids.Count);

            return ids.Count;
        }
        catch(Exception ex)
        {
          _logger.LogError(ex, "Error");
          throw;
        }
    }
}
