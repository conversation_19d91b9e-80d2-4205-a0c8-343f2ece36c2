using OracleDB_MongoDB_Importer.Models;

namespace OracleDB_MongoDB_Importer.Strategies;

public interface IImportStrategyFactory
{
    IImportStrategy CreateStrategy(string syncStrategy);
}

public class ImportStrategyFactory : IImportStrategyFactory
{
    private readonly IServiceProvider _serviceProvider;
    private readonly ILogger<ImportStrategyFactory> _logger;

    public ImportStrategyFactory(IServiceProvider serviceProvider, ILogger<ImportStrategyFactory> logger)
    {
        _serviceProvider = serviceProvider;
        _logger = logger;
    }

    public IImportStrategy CreateStrategy(string syncStrategy)
    {
        return syncStrategy.ToLowerInvariant() switch
        {
            "completereplace" => _serviceProvider.GetRequiredService<CompleteReplaceStrategy>(),
            "initialimport" => _serviceProvider.GetRequiredService<InitialImportStrategy>(),
            "partialimport" => _serviceProvider.GetRequiredService<PartialImport>(),
            _ => throw new ArgumentException($"Unbekannte SyncStrategy: {syncStrategy}", nameof(syncStrategy))
        };
    }
}
