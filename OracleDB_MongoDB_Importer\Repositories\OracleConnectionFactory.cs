using Oracle.ManagedDataAccess.Client;
using OracleDB_MongoDB_Importer.Models;

namespace OracleDB_MongoDB_Importer.Factories;

public class OracleConnectionFactory : IOracleConnectionFactory
{
    private readonly IConfiguration _configuration;

    public OracleConnectionFactory(IConfiguration configuration)
    {
        _configuration = configuration;
    }

    public OracleConnection CreateConnection(string customerId)
    {
        var customer = GetCustomerConfig(customerId);
        var connectionString = customer.OracleConnectionStringTemplate.Replace("{password}", GetOraclePassword());
        var builder = new OracleConnectionStringBuilder(connectionString)
        {
            Pooling = true,
            StatementCacheSize = 0
        };

        return new OracleConnection(builder.ConnectionString);
    }

    private CustomerConfig GetCustomerConfig(string customerId)
    {
        var customers = _configuration.GetSection("Customers").Get<CustomerConfig[]>() ?? [];
        var customer = customers.FirstOrDefault(c => c.CustomerId == customerId);

        if (customer == null)
        {
            throw new ArgumentException($"Kunde mit ID '{customerId}' nicht gefunden", nameof(customerId));
        }

        return customer;
    }

    private string GetOraclePassword()
    {
        var password = Environment.GetEnvironmentVariable("ORACLEDB_PASSWORD");


        return password ?? throw new InvalidOperationException($"Oracle-Passwort nicht gefunden.");
    }
}
