using OracleDB_MongoDB_Importer.Repositories;
using OracleDB_MongoDB_Importer.Services;

namespace OracleDB_MongoDB_Importer;

public class Worker : BackgroundService
{
    private readonly ILogger<Worker> _logger;
    private readonly IServiceProvider _serviceProvider;
    private readonly IConfiguration _configuration;

    public Worker(
        ILogger<Worker> logger,
        IServiceProvider serviceProvider,
        IConfiguration configuration
        )
    {
        _logger = logger;
        _serviceProvider = serviceProvider;
        _configuration = configuration;
    }

    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        _logger.LogInformation("Worker gestartet. Globale Jobs werden über TickerQ verwaltet.");

        while (!stoppingToken.IsCancellationRequested)
        {
            var monitoringRepository = _serviceProvider.GetRequiredService<IMonitoringRepository>();
            var isConnected = await monitoringRepository.TestConnectionAsync(stoppingToken);
            if (!isConnected)
            {
                _logger.LogError("MongoDB Verbindung fehlgeschlagen");
            }

            await Task.Delay(60000, stoppingToken);
        }

        _logger.LogInformation("Worker beendet.");
    }


}
