    <Project Sdk="Microsoft.NET.Sdk.Web">

    <PropertyGroup>
        <TargetFramework>net9.0</TargetFramework>
        <Nullable>enable</Nullable>
        <ImplicitUsings>enable</ImplicitUsings>
        <UserSecretsId>dotnet-OracleDB_MongoDB_Importer-1f1ded21-7e67-4a2f-9e7f-a037c286c72d</UserSecretsId>
        <DockerDefaultTargetOS>Linux</DockerDefaultTargetOS>
    </PropertyGroup>

    <ItemGroup>
        <PackageReference Include="Microsoft.Extensions.Hosting" Version="9.0.7"/>
        <PackageReference Include="TickerQ" Version="2.4.2" />
        <PackageReference Include="TickerQ.Dashboard" Version="2.4.2" />
        <PackageReference Include="Oracle.ManagedDataAccess.Core" Version="23.7.0" />
        <PackageReference Include="MongoDB.Driver" Version="3.0.0" />
        <PackageReference Include="TickerQ.Utilities" Version="2.4.2" />
    </ItemGroup>

    <ItemGroup>
      <Content Include="..\.dockerignore">
        <Link>.dockerignore</Link>
      </Content>
    </ItemGroup>
</Project>
