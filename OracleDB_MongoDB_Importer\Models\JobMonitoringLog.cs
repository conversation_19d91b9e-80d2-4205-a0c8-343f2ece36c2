using MongoDB.Bson;
using MongoDB.Bson.Serialization.Attributes;

namespace OracleDB_MongoDB_Importer.Models;

public class JobMonitoringLog
{
    [BsonId]
    [BsonRepresentation(BsonType.ObjectId)]
    public string Id { get; set; } = string.Empty;

    [BsonElement("jobId")]
    public string JobId { get; set; } = string.Empty;

    [BsonElement("customerId")]
    public string CustomerId { get; set; } = string.Empty;

    [BsonElement("oracleViewName")]
    public string OracleViewName { get; set; } = string.Empty;

    [BsonElement("mongoCollectionName")]
    public string MongoCollectionName { get; set; } = string.Empty;

    [BsonElement("startTime")]
    public DateTime StartTime { get; set; }

    [BsonElement("endTime")]
    public DateTime? EndTime { get; set; }

    [BsonElement("durationMs")]
    public long? DurationMs { get; set; }

    [BsonElement("errorMessage")]
    public string? ErrorMessage { get; set; }

    [BsonElement("documentCount")]
    public long? DocumentCount { get; set; }

    [BsonElement("createdAt")]
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
}
