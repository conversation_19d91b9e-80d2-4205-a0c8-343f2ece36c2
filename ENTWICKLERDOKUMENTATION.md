# OracleDB MongoDB Importer - Entwicklerdokumentation

## Architektur-Überblick

Der OracleDB MongoDB Importer ist eine .NET 9.0 Web-Anwen<PERSON><PERSON>, die auf einer sauberen, schichtbasierten Architektur basiert. Die Anwendung implementiert verschiedene Design Patterns und folgt SOLID-Prinzipien.

### Technologie-Stack

- **.NET 9.0** - Basis-Framework
- **ASP.NET Core** - Web-Framework
- **Oracle.ManagedDataAccess.Core 23.7.0** - Oracle-Datenbankzugriff
- **MongoDB.Driver 3.0.0** - MongoDB-Integration
- **TickerQ 2.4.2** - Job-Scheduling und Dashboard
- **Docker** - Containerisierung

## Projektstruktur

```
OracleDB_MongoDB_Importer/
├── Controllers/           # API-Controller
├── Models/               # Datenmodelle und DTOs
├── Repositories/         # Datenzugriffs-Schicht
├── Services/            # Business-Logic-Schicht
├── Strategies/          # Import-Strategien (Strategy Pattern)
├── Factories/           # Factory Pattern Implementierungen
├── Program.cs           # Anwendungs-Einstiegspunkt
├── Worker.cs            # Background-Service
└── appsettings.json     # Konfiguration
```

## Design Patterns

### 1. Repository Pattern

Abstrahiert den Datenzugriff für Oracle und MongoDB:

```csharp
public interface IOracleRepository
{
    Task<bool> TestConnectionAsync(string customerId);
    IAsyncEnumerable<BsonDocument> GetDataStreamAsync(string customerId, string viewName, CancellationToken cancellationToken);
    Task<List<object>> GetModifiedIdsSinceAsync(string viewName, string idField, string timestampField, string customerId, DateTime? lastTimestamp, CancellationToken cancellationToken);
}

public interface IMongoRepository
{
    Task<bool> TestConnectionAsync(string customerId);
    Task InsertDocumentStreamAsync(string customerId, string collectionName, IAsyncEnumerable<BsonDocument> documentStream, int bufferSize, CancellationToken cancellationToken);
    Task<DateTime?> GetLastUpdateTimestampAsync(string customerId, string collectionName, string timestampField, CancellationToken cancellationToken);
}
```

### 2. Strategy Pattern

Verschiedene Import-Strategien für unterschiedliche Synchronisationsanforderungen:

```csharp
public interface IImportStrategy
{
    Task<long> ExecuteAsync(string customerId, JobConfig jobConfig, CancellationToken cancellationToken);
}

// Implementierungen:
// - CompleteReplaceStrategy: Vollständiger Ersatz der Collection
// - InitialImportStrategy: Erstmaliger Import aller Daten
// - PartialImport: Inkrementeller Import basierend auf Timestamps
```

### 3. Factory Pattern

Erstellt Verbindungen und Strategien:

```csharp
public interface IOracleConnectionFactory
{
    OracleConnection CreateConnection(string customerId);
}

public interface IImportStrategyFactory
{
    IImportStrategy CreateStrategy(string syncStrategy);
}
```

## Dependency Injection Container

Die Anwendung nutzt den eingebauten .NET DI-Container:

```csharp
// Repositories
builder.Services.AddSingleton<IOracleConnectionFactory, OracleConnectionFactory>();
builder.Services.AddTransient<IOracleRepository, OracleRepository>();
builder.Services.AddSingleton<IMongoRepository, MongoRepository>();
builder.Services.AddSingleton<IMonitoringRepository, MonitoringRepository>();

// Services
builder.Services.AddTransient<IOracleTypeConversionService, OracleTypeConversionService>();
builder.Services.AddTransient<IImportService, ImportService>();
builder.Services.AddSingleton<JobMonitoringService>();

// Strategies
builder.Services.AddScoped<CompleteReplaceStrategy>();
builder.Services.AddScoped<InitialImportStrategy>();
builder.Services.AddScoped<PartialImport>();
builder.Services.AddScoped<IImportStrategyFactory, ImportStrategyFactory>();

// TickerQ Integration
builder.Services.AddTickerQ(opt =>
{
    opt.AddDashboard(basePath: "/dashboard");
    opt.AddDashboardBasicAuth();
});
```

## Datenmodelle

### JobConfig

Definiert die Konfiguration für Import-Jobs:

```csharp
public class JobConfig
{
    public string JobId { get; set; }
    public string OracleViewName { get; set; }
    public string MongoCollectionName { get; set; }
    public string SyncStrategy { get; set; }
    public string? TimestampField { get; set; }
    public string? IdField { get; set; }
    public int InitialImportBatchSize { get; set; } = 1000;
    public int SyncBatchSize { get; set; } = 500;
    public bool IsEnabled { get; set; } = true;
}
```

### CustomerConfig

Definiert Kunden-spezifische Konfigurationen:

```csharp
public class CustomerConfig
{
    public string CustomerId { get; set; }
    public string OracleConnectionStringTemplate { get; set; }
    public string MongoDbConnectionStringTemplate { get; set; }
    public string MongoDbDatabaseName { get; set; }
    public bool IsEnabled { get; set; } = true;
}
```

### JobMonitoringLog

Protokolliert Job-Ausführungen:

```csharp
public class JobMonitoringLog
{
    [BsonId]
    public string Id { get; set; }
    public string JobId { get; set; }
    public string CustomerId { get; set; }
    public DateTime StartTime { get; set; }
    public DateTime? EndTime { get; set; }
    public long? DurationMs { get; set; }
    public string? ErrorMessage { get; set; }
    public long? DocumentCount { get; set; }
}
```

## Import-Strategien im Detail

### CompleteReplaceStrategy

- **Verwendung**: Für Daten ohne Timestamp-Feld oder bei vollständiger Neusynchronisation
- **Ablauf**:
  1. Löscht die gesamte MongoDB-Collection
  2. Lädt alle Daten aus der Oracle-View
  3. Fügt alle Daten in die MongoDB-Collection ein

### InitialImportStrategy

- **Verwendung**: Beim ersten Import einer neuen Collection
- **Ablauf**:
  1. Lädt alle Daten aus der Oracle-View
  2. Fügt alle Daten in die leere MongoDB-Collection ein
  3. Erstellt notwendige Indizes

### PartialImport

- **Verwendung**: Für inkrementelle Updates basierend auf Timestamps
- **Ablauf**:
  1. Ermittelt den letzten Timestamp aus MongoDB
  2. Lädt nur geänderte IDs seit dem letzten Update
  3. Löscht veraltete Dokumente aus MongoDB
  4. Fügt aktualisierte Dokumente ein

## Streaming und Performance

### Asynchrone Datenströme

Die Anwendung nutzt `IAsyncEnumerable<T>` für speichereffiziente Datenverarbeitung:

```csharp
public async IAsyncEnumerable<BsonDocument> GetDataStreamAsync(
    string customerId,
    string viewName,
    [EnumeratorCancellation] CancellationToken cancellationToken = default)
{
    using var connection = await GetConnectionAsync(customerId);
    using var command = new OracleCommand($"SELECT * FROM {viewName}", connection);
    using var reader = await command.ExecuteReaderAsync(cancellationToken);

    while (await reader.ReadAsync(cancellationToken))
    {
        yield return ConvertToBsonDocument(reader);
    }
}
```

### Batch-Verarbeitung

Daten werden in konfigurierbaren Batch-Größen verarbeitet:

```csharp
await _mongoRepository.InsertDocumentStreamAsync(
    customerId,
    jobConfig.MongoCollectionName,
    documentStream,
    jobConfig.InitialImportBatchSize, // Batch-Größe
    cancellationToken);
```

## Typ-Konvertierung

### Oracle zu MongoDB Mapping

Die `OracleTypeConversionService` behandelt Typ-Konvertierungen:

```csharp
private static readonly Dictionary<string, Func<OracleDataReader, int, BsonValue>> TypeMappingToBson = new()
{
    { "NUMBER", ConvertNumberToBson },
    { "VARCHAR2", ConvertStringToBson },
    { "DATE", ConvertDateToBson },
    { "TIMESTAMP", ConvertTimestampToBson },
    { "CLOB", ConvertClobToBson }
};
```

## Job-Scheduling mit TickerQ

### Cron-basierte Jobs

Jobs werden über TickerQ-Attribute definiert:

```csharp
[TickerFunction(functionName: "KommSync_storelogix_sendsally", cronExpression: "%cronExpressions:normaleTabellen%")]
public async Task ExecuteKommSyncJob_SendSally(CancellationToken cancellationToken)
{
    await ExecuteJobForSpecificCustomer("V_EXP_KOMM_To_storelogix_komm", "storelogix_sendsally", cancellationToken);
}
```

### Dynamische Job-Generierung

Jobs werden für jeden Kunden automatisch generiert basierend auf der Konfiguration.

## Verbindungsmanagement

### Connection Pooling

- **Oracle**: Nutzt eingebautes Connection Pooling
- **MongoDB**: Verwendet Connection-Caching mit `ConcurrentDictionary`

### Fehlerbehandlung

Robuste Fehlerbehandlung mit Retry-Mechanismen und detailliertem Logging.

## Monitoring und Logging

### Strukturiertes Logging

```csharp
_logger.LogInformation("Job {JobId} erfolgreich abgeschlossen mit Strategie {ActualStrategy}, {DocumentCount} Dokumente verarbeitet",
    jobConfig.JobId, actualStrategy, documentCount);
```

### Performance-Metriken

- Job-Laufzeiten
- Verarbeitete Dokumentanzahl
- Fehlerstatistiken
- Verbindungsstatistiken

## Konfiguration

### Umgebungsvariablen

- `ORACLEDB_PASSWORD`: Oracle-Datenbankpasswort
- `MONGODB_PASSWORD`: MongoDB-Passwort
- `JOB_MONITORING`: Aktiviert/deaktiviert Job-Monitoring

### Konfigurationsdateien

- `appsettings.json`: Basis-Konfiguration
- `appsettings.Development.json`: Entwicklungsumgebung
- `appsettings.MongoDBOptimization.json`: MongoDB-Optimierungen

## API-Endpunkte

### Health Check

```http
GET /health
Response: "OK"
```

### Manueller Import

```http
GET /api/ManualImport/initial-import-all
Response: {
  "message": "Initial Import für alle Kunden und Jobs abgeschlossen",
  "totalJobs": 120,
  "successfulJobs": 118,
  "failedJobs": 2,
  "results": [...]
}
```

## Entwicklungsrichtlinien

### Code-Standards

- Async/Await für alle I/O-Operationen
- CancellationToken für alle async-Methoden
- Strukturiertes Logging mit Template-Parametern
- Dependency Injection für alle Abhängigkeiten

### Testing-Strategien

- Unit Tests für Business Logic
- Integration Tests für Repository-Schicht
- End-to-End Tests für komplette Import-Workflows

### Performance-Optimierungen

- Streaming für große Datenmengen
- Batch-Verarbeitung
- Connection Pooling
- Asynchrone Verarbeitung

## Deployment

### Docker-Deployment

```dockerfile
FROM mcr.microsoft.com/dotnet/aspnet:9.0 AS final
WORKDIR /app
COPY --from=build /app/publish .
EXPOSE 8080
ENV ASPNETCORE_ENVIRONMENT=Production
ENV ASPNETCORE_URLS=http://+:8080
ENTRYPOINT ["dotnet", "OracleDB_MongoDB_Importer.dll"]
```

### Umgebungsvariablen für Production

```bash
ORACLEDB_PASSWORD=<oracle_password>
MONGODB_PASSWORD=<mongodb_password>
JOB_MONITORING=true
ASPNETCORE_ENVIRONMENT=Production
```

### Health Checks

Die Anwendung bietet Health Checks für Monitoring:

- HTTP Health Check: `/health`
- Docker Health Check: Automatische Überwachung der Anwendung

## Erweiterte Konfiguration

### Cron-Ausdrücke

```json
{
  "CronExpressions": {
    "normaleTabellen": "*/15 * * * *", // Alle 15 Minuten
    "taeglicheTabellen": "0 6 * * *", // Täglich um 6:00 Uhr
    "woechentlich": "0 2 * * 0", // Sonntags um 2:00 Uhr
    "monatlich": "0 1 1 * *" // Monatlich am 1. um 1:00 Uhr
  }
}
```

### MongoDB-Optimierungen

```json
{
  "MongoDbSettings": {
    "connectTimeoutMS": 60000,
    "maxIdleTimeMS": 120000,
    "socketTimeoutMS": 120000,
    "serverSelectionTimeoutMS": 30000,
    "heartbeatFrequencyMS": 20000,
    "maxPoolSize": 50,
    "minPoolSize": 5,
    "maxConnecting": 5,
    "waitQueueTimeoutMS": 300000
  }
}
```

## Fehlerbehandlung und Debugging

### Logging-Konfiguration

```json
{
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft.Hosting.Lifetime": "Information",
      "OracleDB_MongoDB_Importer": "Debug",
      "MongoDB.Driver": "Warning",
      "Oracle.ManagedDataAccess": "Warning"
    }
  }
}
```

### Häufige Debugging-Szenarien

#### 1. Verbindungsprobleme

```csharp
// Oracle-Verbindung testen
var oracleRepo = serviceProvider.GetService<IOracleRepository>();
var isOracleConnected = await oracleRepo.TestConnectionAsync("customerId");

// MongoDB-Verbindung testen
var mongoRepo = serviceProvider.GetService<IMongoRepository>();
var isMongoConnected = await mongoRepo.TestConnectionAsync("customerId");
```

#### 2. Job-Ausführung debuggen

```csharp
// Job-Status überprüfen
var jobMonitoring = serviceProvider.GetService<JobMonitoringService>();
var activeJobs = jobMonitoring.GetActiveJobs();
var isJobActive = jobMonitoring.IsJobActive("jobId");
```

#### 3. Datentyp-Konvertierung debuggen

```csharp
// Typ-Konvertierung testen
var conversionService = serviceProvider.GetService<IOracleTypeConversionService>();
// Breakpoints in ConvertToBsonDocument setzen
```

## Sicherheit

### Passwort-Management

- Passwörter werden über Umgebungsvariablen verwaltet
- Keine Klartext-Passwörter in Konfigurationsdateien
- Connection String Templates mit Platzhaltern

### Dashboard-Authentifizierung

```json
{
  "TickerQBasicAuth": {
    "Username": "admin",
    "Password": "secure_password_here"
  }
}
```

### Netzwerk-Sicherheit

- HTTPS in Production empfohlen
- Firewall-Regeln für Oracle/MongoDB-Zugriff
- VPN-Verbindungen für Produktionsumgebungen

## Wartung und Monitoring

### Regelmäßige Wartungsaufgaben

1. **Log-Rotation**: Alte Logs archivieren/löschen
2. **Performance-Monitoring**: Dashboard-Metriken überwachen
3. **Datenbankwartung**: Index-Optimierung, Statistiken aktualisieren
4. **Backup-Verifikation**: MongoDB-Backups testen

### Monitoring-Metriken

- Job-Erfolgsrate
- Durchschnittliche Job-Laufzeit
- Datenvolumen pro Job
- Fehlerrate nach Kunde/Job
- Verbindungspool-Auslastung

### Alerting

Empfohlene Alerts:

- Job-Fehler > 5% in 1 Stunde
- Job-Laufzeit > 2x Durchschnitt
- Verbindungsfehler zu Oracle/MongoDB
- Speicherverbrauch > 80%

## Erweiterungen und Anpassungen

### Neue Import-Strategie hinzufügen

1. Interface `IImportStrategy` implementieren
2. In `ImportStrategyFactory` registrieren
3. DI-Container konfigurieren
4. Tests schreiben

```csharp
public class CustomImportStrategy : IImportStrategy
{
    public async Task<long> ExecuteAsync(string customerId, JobConfig jobConfig, CancellationToken cancellationToken)
    {
        // Custom logic here
        return documentCount;
    }
}
```

### Neue Datenquelle hinzufügen

1. Repository-Interface definieren
2. Repository implementieren
3. Connection Factory erstellen
4. DI-Container erweitern

### Custom TickerQ Functions

```csharp
[TickerFunction(functionName: "CustomJob", cronExpression: "0 */2 * * *")]
public async Task ExecuteCustomJob(CancellationToken cancellationToken)
{
    // Custom job logic
}
```

## Troubleshooting

### Performance-Probleme

1. **Langsame Oracle-Queries**: Indizes überprüfen, Query-Pläne analysieren
2. **MongoDB-Timeouts**: Connection Pool Settings anpassen
3. **Memory-Leaks**: Streaming-Implementation überprüfen
4. **CPU-Auslastung**: Batch-Größen reduzieren

### Dateninkonsistenzen

1. **Fehlende Daten**: Partial Import Timestamps überprüfen
2. **Doppelte Daten**: ID-Felder und Unique-Constraints prüfen
3. **Typ-Konvertierungsfehler**: Oracle-Schema vs. MongoDB-Mapping

### Deployment-Probleme

1. **Container startet nicht**: Logs überprüfen, Umgebungsvariablen validieren
2. **Dashboard nicht erreichbar**: Port-Mapping und Firewall prüfen
3. **Jobs werden nicht ausgeführt**: TickerQ-Konfiguration überprüfen

## Best Practices

### Code-Qualität

- Verwende `ConfigureAwait(false)` für Library-Code
- Implementiere `IDisposable` für Resource-Management
- Nutze `CancellationToken` konsequent
- Verwende strukturiertes Logging

### Performance

- Streaming für große Datenmengen
- Asynchrone Verarbeitung
- Connection Pooling
- Batch-Operationen

### Wartbarkeit

- Klare Trennung der Schichten
- Dependency Injection
- Konfiguration externalisieren
- Umfassende Tests

### Sicherheit

- Secrets Management
- Input Validation
- Error Handling ohne Information Disclosure
- Audit Logging
