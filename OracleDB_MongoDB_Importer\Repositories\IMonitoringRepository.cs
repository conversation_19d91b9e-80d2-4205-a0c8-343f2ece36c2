using OracleDB_MongoDB_Importer.Models;

namespace OracleDB_MongoDB_Importer.Repositories;

public interface IMonitoringRepository
{
    Task SaveJobMonitoringLogAsync(JobMonitoringLog log, CancellationToken cancellationToken = default);
    Task UpdateJobMonitoringLogAsync(JobMonitoringLog log, CancellationToken cancellationToken = default);
    Task<JobMonitoringLog?> GetJobMonitoringLogAsync(string jobId, CancellationToken cancellationToken = default);
    Task<bool> TestConnectionAsync(CancellationToken cancellationToken = default);
}
