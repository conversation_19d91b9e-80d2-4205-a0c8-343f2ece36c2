using OracleDB_MongoDB_Importer.Models;
using OracleDB_MongoDB_Importer.Repositories;

namespace OracleDB_MongoDB_Importer.Strategies;

public class InitialImportStrategy : IImportStrategy
{
    private readonly IOracleRepository _oracleRepository;
    private readonly IMongoRepository _mongoRepository;
    private readonly ILogger<InitialImportStrategy> _logger;

    public InitialImportStrategy(
        IOracleRepository oracleRepository,
        IMongoRepository mongoRepository,
        ILogger<InitialImportStrategy> logger)
    {
        _oracleRepository = oracleRepository;
        _mongoRepository = mongoRepository;
        _logger = logger;
    }

    public async Task<long> ExecuteAsync(string customerId, JobConfig jobConfig, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Starte InitialImport für Job {JobId}, View: {ViewName}, Collection: {CollectionName}",
            jobConfig.JobId, jobConfig.OracleViewName, jobConfig.MongoCollectionName);

        try
        {
            var documentStream = _oracleRepository.GetDataStreamAsync(customerId, jobConfig.OracleViewName, cancellationToken);

            await _mongoRepository.InsertDocumentStreamAsync(
                customerId,
                jobConfig.MongoCollectionName,
                documentStream,
                jobConfig.InitialImportBatchSize,
                cancellationToken);

            var finalCount = await _mongoRepository.GetDocumentCountAsync(customerId, jobConfig.MongoCollectionName);
            _logger.LogInformation("InitialImport erfolgreich abgeschlossen. {FinalCount} Dokumente in Collection {CollectionName}",
                finalCount, jobConfig.MongoCollectionName);

            return finalCount;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Fehler beim Stream-basierten InitialImport für Job {JobId}", jobConfig.JobId);
            throw;
        }
    }
}
