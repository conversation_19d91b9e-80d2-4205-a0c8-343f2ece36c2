using OracleDB_MongoDB_Importer.Models;
using TickerQ.Utilities.Base;

namespace OracleDB_MongoDB_Importer.Services;

public class GlobalJobTickerService
{
    private readonly IImportService _importService;
    private readonly IConfiguration _configuration;
    private readonly ILogger<GlobalJobTickerService> _logger;

    public GlobalJobTickerService(
        IImportService importService,
        IConfiguration configuration,
        ILogger<GlobalJobTickerService> logger)
    {
        _importService = importService;
        _configuration = configuration;
        _logger = logger;
    }

    [TickerFunction(functionName: "KommSync_storelogix_sendsally", cronExpression: "%cronExpressions:normaleTabellen%")]
    public async Task ExecuteKommSyncJob_SendSally(CancellationToken cancellationToken)
    {
        await ExecuteJobForSpecificCustomer("V_EXP_KOMM_To_storelogix_komm", "storelogix_sendsally", cancellationToken);
    }

    [TickerFunction(functionName: "KommSync_storelogix_vertrieb2", cronExpression: "%cronExpressions:normaleTabellen%")]
    public async Task ExecuteKommSyncJob_Vertrieb2(CancellationToken cancellationToken)
    {
        await ExecuteJobForSpecificCustomer("V_EXP_KOMM_To_storelogix_komm", "storelogix_vertrieb2", cancellationToken);
    }

    [TickerFunction(functionName: "KommSync_storelogix_cella", cronExpression: "%cronExpressions:normaleTabellen%")]
    public async Task ExecuteKommSyncJob_Cella(CancellationToken cancellationToken)
    {
        await ExecuteJobForSpecificCustomer("V_EXP_KOMM_To_storelogix_komm", "storelogix_cella", cancellationToken);
    }

    [TickerFunction(functionName: "KommSync_storelogix_kaufland", cronExpression: "%cronExpressions:normaleTabellen%")]
    public async Task ExecuteKommSyncJob_Kaufland(CancellationToken cancellationToken)
    {
        await ExecuteJobForSpecificCustomer("V_EXP_KOMM_To_storelogix_komm", "storelogix_kaufland", cancellationToken);
    }

    [TickerFunction(functionName: "KommSync_storelogix_transco", cronExpression: "%cronExpressions:normaleTabellen%")]
    public async Task ExecuteKommSyncJob_Transco(CancellationToken cancellationToken)
    {
        await ExecuteJobForSpecificCustomer("V_EXP_KOMM_To_storelogix_komm", "storelogix_transco", cancellationToken);
    }


    [TickerFunction(functionName: "KommSync_storelogix_zen", cronExpression: "%cronExpressions:normaleTabellen%")]
    public async Task ExecuteKommSyncJob_Zen(CancellationToken cancellationToken)
    {
        await ExecuteJobForSpecificCustomer("V_EXP_KOMM_To_storelogix_komm", "storelogix_zen", cancellationToken);
    }

    [TickerFunction(functionName: "KommSync_storelogix_ffs", cronExpression: "%cronExpressions:normaleTabellen%")]
    public async Task ExecuteKommSyncJob_Ffs(CancellationToken cancellationToken)
    {
        await ExecuteJobForSpecificCustomer("V_EXP_KOMM_To_storelogix_komm", "storelogix_ffs", cancellationToken);
    }

    [TickerFunction(functionName: "KommSync_storelogix_dfs", cronExpression: "%cronExpressions:normaleTabellen%")]
    public async Task ExecuteKommSyncJob_Dfs(CancellationToken cancellationToken)
    {
        await ExecuteJobForSpecificCustomer("V_EXP_KOMM_To_storelogix_komm", "storelogix_dfs", cancellationToken);
    }

    [TickerFunction(functionName: "KommSync_storelogix_schroeder", cronExpression: "%cronExpressions:normaleTabellen%")]
    public async Task ExecuteKommSyncJob_Schroeder(CancellationToken cancellationToken)
    {
        await ExecuteJobForSpecificCustomer("V_EXP_KOMM_To_storelogix_komm", "storelogix_schroeder", cancellationToken);
    }


    [TickerFunction(functionName: "KommSync_storelogix_petromax", cronExpression: "%cronExpressions:normaleTabellen%")]
    public async Task ExecuteKommSyncJob_Petromax(CancellationToken cancellationToken)
    {
        await ExecuteJobForSpecificCustomer("V_EXP_KOMM_To_storelogix_komm", "storelogix_petromax", cancellationToken);
    }



    [TickerFunction(functionName: "AuftragSync_storelogix_sendsally", cronExpression: "%cronExpressions:normaleTabellen%")]
    public async Task ExecuteAuftragSyncJob_SendSally(CancellationToken cancellationToken)
    {
        await ExecuteJobForSpecificCustomer("V_EXP_AUFTRAG_VERSENDET_To_storelogix_orders_sent", "storelogix_sendsally", cancellationToken);
    }

    [TickerFunction(functionName: "AuftragSync_storelogix_vertrieb2", cronExpression: "%cronExpressions:normaleTabellen%")]
    public async Task ExecuteAuftragSyncJob_Vertrieb2(CancellationToken cancellationToken)
    {
        await ExecuteJobForSpecificCustomer("V_EXP_AUFTRAG_VERSENDET_To_storelogix_orders_sent", "storelogix_vertrieb2", cancellationToken);
    }

    [TickerFunction(functionName: "AuftragSync_storelogix_cella", cronExpression: "%cronExpressions:normaleTabellen%")]
    public async Task ExecuteAuftragSyncJob_Cella(CancellationToken cancellationToken)
    {
        await ExecuteJobForSpecificCustomer("V_EXP_AUFTRAG_VERSENDET_To_storelogix_orders_sent", "storelogix_cella", cancellationToken);
    }

    [TickerFunction(functionName: "AuftragSync_storelogix_kaufland", cronExpression: "%cronExpressions:normaleTabellen%")]
    public async Task ExecuteAuftragSyncJob_Kaufland(CancellationToken cancellationToken)
    {
        await ExecuteJobForSpecificCustomer("V_EXP_AUFTRAG_VERSENDET_To_storelogix_orders_sent", "storelogix_kaufland", cancellationToken);
    }

    [TickerFunction(functionName: "AuftragSync_storelogix_transco", cronExpression: "%cronExpressions:normaleTabellen%")]
    public async Task ExecuteAuftragSyncJob_Transco(CancellationToken cancellationToken)
    {
        await ExecuteJobForSpecificCustomer("V_EXP_AUFTRAG_VERSENDET_To_storelogix_orders_sent", "storelogix_transco", cancellationToken);
    }


    [TickerFunction(functionName: "AuftragSync_storelogix_zen", cronExpression: "%cronExpressions:normaleTabellen%")]
    public async Task ExecuteAuftragSyncJob_Zen(CancellationToken cancellationToken)
    {
        await ExecuteJobForSpecificCustomer("V_EXP_AUFTRAG_VERSENDET_To_storelogix_orders_sent", "storelogix_zen", cancellationToken);
    }

    [TickerFunction(functionName: "AuftragSync_storelogix_ffs", cronExpression: "%cronExpressions:normaleTabellen%")]
    public async Task ExecuteAuftragSyncJob_Ffs(CancellationToken cancellationToken)
    {
        await ExecuteJobForSpecificCustomer("V_EXP_AUFTRAG_VERSENDET_To_storelogix_orders_sent", "storelogix_ffs", cancellationToken);
    }

    [TickerFunction(functionName: "AuftragSync_storelogix_dfs", cronExpression: "%cronExpressions:normaleTabellen%")]
    public async Task ExecuteAuftragSyncJob_Dfs(CancellationToken cancellationToken)
    {
        await ExecuteJobForSpecificCustomer("V_EXP_AUFTRAG_VERSENDET_To_storelogix_orders_sent", "storelogix_dfs", cancellationToken);
    }

    [TickerFunction(functionName: "AuftragSync_storelogix_schroeder", cronExpression: "%cronExpressions:normaleTabellen%")]
    public async Task ExecuteAuftragSyncJob_Schroeder(CancellationToken cancellationToken)
    {
        await ExecuteJobForSpecificCustomer("V_EXP_AUFTRAG_VERSENDET_To_storelogix_orders_sent", "storelogix_schroeder", cancellationToken);
    }


    [TickerFunction(functionName: "AuftragSync_storelogix_petromax", cronExpression: "%cronExpressions:normaleTabellen%")]
    public async Task ExecuteAuftragSyncJob_Petromax(CancellationToken cancellationToken)
    {
        await ExecuteJobForSpecificCustomer("V_EXP_AUFTRAG_VERSENDET_To_storelogix_orders_sent", "storelogix_petromax", cancellationToken);
    }



    [TickerFunction(functionName: "SessionSync_storelogix_sendsally", cronExpression: "%cronExpressions:normaleTabellen%")]
    public async Task ExecuteSessionSyncJob_SendSally(CancellationToken cancellationToken)
    {
        await ExecuteJobForSpecificCustomer("V_EXP_SESSION_NOW_To_storelogix_session_now", "storelogix_sendsally", cancellationToken);
    }

    [TickerFunction(functionName: "SessionSync_storelogix_vertrieb2", cronExpression: "%cronExpressions:normaleTabellen%")]
    public async Task ExecuteSessionSyncJob_Vertrieb2(CancellationToken cancellationToken)
    {
        await ExecuteJobForSpecificCustomer("V_EXP_SESSION_NOW_To_storelogix_session_now", "storelogix_vertrieb2", cancellationToken);
    }

    [TickerFunction(functionName: "SessionSync_storelogix_cella", cronExpression: "%cronExpressions:normaleTabellen%")]
    public async Task ExecuteSessionSyncJob_Cella(CancellationToken cancellationToken)
    {
        await ExecuteJobForSpecificCustomer("V_EXP_SESSION_NOW_To_storelogix_session_now", "storelogix_cella", cancellationToken);
    }

    [TickerFunction(functionName: "SessionSync_storelogix_kaufland", cronExpression: "%cronExpressions:normaleTabellen%")]
    public async Task ExecuteSessionSyncJob_Kaufland(CancellationToken cancellationToken)
    {
        await ExecuteJobForSpecificCustomer("V_EXP_SESSION_NOW_To_storelogix_session_now", "storelogix_kaufland", cancellationToken);
    }

    [TickerFunction(functionName: "SessionSync_storelogix_transco", cronExpression: "%cronExpressions:normaleTabellen%")]
    public async Task ExecuteSessionSyncJob_Transco(CancellationToken cancellationToken)
    {
        await ExecuteJobForSpecificCustomer("V_EXP_SESSION_NOW_To_storelogix_session_now", "storelogix_transco", cancellationToken);
    }


    [TickerFunction(functionName: "SessionSync_storelogix_zen", cronExpression: "%cronExpressions:normaleTabellen%")]
    public async Task ExecuteSessionSyncJob_Zen(CancellationToken cancellationToken)
    {
        await ExecuteJobForSpecificCustomer("V_EXP_SESSION_NOW_To_storelogix_session_now", "storelogix_zen", cancellationToken);
    }

    [TickerFunction(functionName: "SessionSync_storelogix_ffs", cronExpression: "%cronExpressions:normaleTabellen%")]
    public async Task ExecuteSessionSyncJob_Ffs(CancellationToken cancellationToken)
    {
        await ExecuteJobForSpecificCustomer("V_EXP_SESSION_NOW_To_storelogix_session_now", "storelogix_ffs", cancellationToken);
    }

    [TickerFunction(functionName: "SessionSync_storelogix_dfs", cronExpression: "%cronExpressions:normaleTabellen%")]
    public async Task ExecuteSessionSyncJob_Dfs(CancellationToken cancellationToken)
    {
        await ExecuteJobForSpecificCustomer("V_EXP_SESSION_NOW_To_storelogix_session_now", "storelogix_dfs", cancellationToken);
    }

    [TickerFunction(functionName: "SessionSync_storelogix_schroeder", cronExpression: "%cronExpressions:normaleTabellen%")]
    public async Task ExecuteSessionSyncJob_Schroeder(CancellationToken cancellationToken)
    {
        await ExecuteJobForSpecificCustomer("V_EXP_SESSION_NOW_To_storelogix_session_now", "storelogix_schroeder", cancellationToken);
    }


    [TickerFunction(functionName: "SessionSync_storelogix_petromax", cronExpression: "%cronExpressions:normaleTabellen%")]
    public async Task ExecuteSessionSyncJob_Petromax(CancellationToken cancellationToken)
    {
        await ExecuteJobForSpecificCustomer("V_EXP_SESSION_NOW_To_storelogix_session_now", "storelogix_petromax", cancellationToken);
    }



    [TickerFunction(functionName: "WareneingangSync_storelogix_sendsally", cronExpression: "%cronExpressions:normaleTabellen%")]
    public async Task ExecuteWareneingangSyncJob_SendSally(CancellationToken cancellationToken)
    {
        await ExecuteJobForSpecificCustomer("V_EXP_WARENEINGANG_To_storelogix_we", "storelogix_sendsally", cancellationToken);
    }

    [TickerFunction(functionName: "WareneingangSync_storelogix_vertrieb2", cronExpression: "%cronExpressions:normaleTabellen%")]
    public async Task ExecuteWareneingangSyncJob_Vertrieb2(CancellationToken cancellationToken)
    {
        await ExecuteJobForSpecificCustomer("V_EXP_WARENEINGANG_To_storelogix_we", "storelogix_vertrieb2", cancellationToken);
    }

    [TickerFunction(functionName: "WareneingangSync_storelogix_cella", cronExpression: "%cronExpressions:normaleTabellen%")]
    public async Task ExecuteWareneingangSyncJob_Cella(CancellationToken cancellationToken)
    {
        await ExecuteJobForSpecificCustomer("V_EXP_WARENEINGANG_To_storelogix_we", "storelogix_cella", cancellationToken);
    }

    [TickerFunction(functionName: "WareneingangSync_storelogix_kaufland", cronExpression: "%cronExpressions:normaleTabellen%")]
    public async Task ExecuteWareneingangSyncJob_Kaufland(CancellationToken cancellationToken)
    {
        await ExecuteJobForSpecificCustomer("V_EXP_WARENEINGANG_To_storelogix_we", "storelogix_kaufland", cancellationToken);
    }

    [TickerFunction(functionName: "WareneingangSync_storelogix_transco", cronExpression: "%cronExpressions:normaleTabellen%")]
    public async Task ExecuteWareneingangSyncJob_Transco(CancellationToken cancellationToken)
    {
        await ExecuteJobForSpecificCustomer("V_EXP_WARENEINGANG_To_storelogix_we", "storelogix_transco", cancellationToken);
    }


    [TickerFunction(functionName: "WareneingangSync_storelogix_zen", cronExpression: "%cronExpressions:normaleTabellen%")]
    public async Task ExecuteWareneingangSyncJob_Zen(CancellationToken cancellationToken)
    {
        await ExecuteJobForSpecificCustomer("V_EXP_WARENEINGANG_To_storelogix_we", "storelogix_zen", cancellationToken);
    }

    [TickerFunction(functionName: "WareneingangSync_storelogix_ffs", cronExpression: "%cronExpressions:normaleTabellen%")]
    public async Task ExecuteWareneingangSyncJob_Ffs(CancellationToken cancellationToken)
    {
        await ExecuteJobForSpecificCustomer("V_EXP_WARENEINGANG_To_storelogix_we", "storelogix_ffs", cancellationToken);
    }

    [TickerFunction(functionName: "WareneingangSync_storelogix_dfs", cronExpression: "%cronExpressions:normaleTabellen%")]
    public async Task ExecuteWareneingangSyncJob_Dfs(CancellationToken cancellationToken)
    {
        await ExecuteJobForSpecificCustomer("V_EXP_WARENEINGANG_To_storelogix_we", "storelogix_dfs", cancellationToken);
    }

    [TickerFunction(functionName: "WareneingangSync_storelogix_schroeder", cronExpression: "%cronExpressions:normaleTabellen%")]
    public async Task ExecuteWareneingangSyncJob_Schroeder(CancellationToken cancellationToken)
    {
        await ExecuteJobForSpecificCustomer("V_EXP_WARENEINGANG_To_storelogix_we", "storelogix_schroeder", cancellationToken);
    }


    [TickerFunction(functionName: "WareneingangSync_storelogix_petromax", cronExpression: "%cronExpressions:normaleTabellen%")]
    public async Task ExecuteWareneingangSyncJob_Petromax(CancellationToken cancellationToken)
    {
        await ExecuteJobForSpecificCustomer("V_EXP_WARENEINGANG_To_storelogix_we", "storelogix_petromax", cancellationToken);
    }



    [TickerFunction(functionName: "VerladungSync_storelogix_sendsally", cronExpression: "%cronExpressions:normaleTabellen%")]
    public async Task ExecuteVerladungSyncJob_SendSally(CancellationToken cancellationToken)
    {
        await ExecuteJobForSpecificCustomer("V_EXP_VERLADUNG_To_storelogix_verl", "storelogix_sendsally", cancellationToken);
    }

    [TickerFunction(functionName: "VerladungSync_storelogix_vertrieb2", cronExpression: "%cronExpressions:normaleTabellen%")]
    public async Task ExecuteVerladungSyncJob_Vertrieb2(CancellationToken cancellationToken)
    {
        await ExecuteJobForSpecificCustomer("V_EXP_VERLADUNG_To_storelogix_verl", "storelogix_vertrieb2", cancellationToken);
    }

    [TickerFunction(functionName: "VerladungSync_storelogix_cella", cronExpression: "%cronExpressions:normaleTabellen%")]
    public async Task ExecuteVerladungSyncJob_Cella(CancellationToken cancellationToken)
    {
        await ExecuteJobForSpecificCustomer("V_EXP_VERLADUNG_To_storelogix_verl", "storelogix_cella", cancellationToken);
    }

    [TickerFunction(functionName: "VerladungSync_storelogix_kaufland", cronExpression: "%cronExpressions:normaleTabellen%")]
    public async Task ExecuteVerladungSyncJob_Kaufland(CancellationToken cancellationToken)
    {
        await ExecuteJobForSpecificCustomer("V_EXP_VERLADUNG_To_storelogix_verl", "storelogix_kaufland", cancellationToken);
    }

    [TickerFunction(functionName: "VerladungSync_storelogix_transco", cronExpression: "%cronExpressions:normaleTabellen%")]
    public async Task ExecuteVerladungSyncJob_Transco(CancellationToken cancellationToken)
    {
        await ExecuteJobForSpecificCustomer("V_EXP_VERLADUNG_To_storelogix_verl", "storelogix_transco", cancellationToken);
    }


    [TickerFunction(functionName: "VerladungSync_storelogix_zen", cronExpression: "%cronExpressions:normaleTabellen%")]
    public async Task ExecuteVerladungSyncJob_Zen(CancellationToken cancellationToken)
    {
        await ExecuteJobForSpecificCustomer("V_EXP_VERLADUNG_To_storelogix_verl", "storelogix_zen", cancellationToken);
    }

    [TickerFunction(functionName: "VerladungSync_storelogix_ffs", cronExpression: "%cronExpressions:normaleTabellen%")]
    public async Task ExecuteVerladungSyncJob_Ffs(CancellationToken cancellationToken)
    {
        await ExecuteJobForSpecificCustomer("V_EXP_VERLADUNG_To_storelogix_verl", "storelogix_ffs", cancellationToken);
    }

    [TickerFunction(functionName: "VerladungSync_storelogix_dfs", cronExpression: "%cronExpressions:normaleTabellen%")]
    public async Task ExecuteVerladungSyncJob_Dfs(CancellationToken cancellationToken)
    {
        await ExecuteJobForSpecificCustomer("V_EXP_VERLADUNG_To_storelogix_verl", "storelogix_dfs", cancellationToken);
    }

    [TickerFunction(functionName: "VerladungSync_storelogix_schroeder", cronExpression: "%cronExpressions:normaleTabellen%")]
    public async Task ExecuteVerladungSyncJob_Schroeder(CancellationToken cancellationToken)
    {
        await ExecuteJobForSpecificCustomer("V_EXP_VERLADUNG_To_storelogix_verl", "storelogix_schroeder", cancellationToken);
    }


    [TickerFunction(functionName: "VerladungSync_storelogix_petromax", cronExpression: "%cronExpressions:normaleTabellen%")]
    public async Task ExecuteVerladungSyncJob_Petromax(CancellationToken cancellationToken)
    {
        await ExecuteJobForSpecificCustomer("V_EXP_VERLADUNG_To_storelogix_verl", "storelogix_petromax", cancellationToken);
    }



    [TickerFunction(functionName: "RetourenSync_storelogix_sendsally", cronExpression: "%cronExpressions:normaleTabellen%")]
    public async Task ExecuteRetourenSyncJob_SendSally(CancellationToken cancellationToken)
    {
        await ExecuteJobForSpecificCustomer("V_EXP_RETOUREN_To_storelogix_returns", "storelogix_sendsally", cancellationToken);
    }

    [TickerFunction(functionName: "RetourenSync_storelogix_vertrieb2", cronExpression: "%cronExpressions:normaleTabellen%")]
    public async Task ExecuteRetourenSyncJob_Vertrieb2(CancellationToken cancellationToken)
    {
        await ExecuteJobForSpecificCustomer("V_EXP_RETOUREN_To_storelogix_returns", "storelogix_vertrieb2", cancellationToken);
    }

    [TickerFunction(functionName: "RetourenSync_storelogix_cella", cronExpression: "%cronExpressions:normaleTabellen%")]
    public async Task ExecuteRetourenSyncJob_Cella(CancellationToken cancellationToken)
    {
        await ExecuteJobForSpecificCustomer("V_EXP_RETOUREN_To_storelogix_returns", "storelogix_cella", cancellationToken);
    }

    [TickerFunction(functionName: "RetourenSync_storelogix_kaufland", cronExpression: "%cronExpressions:normaleTabellen%")]
    public async Task ExecuteRetourenSyncJob_Kaufland(CancellationToken cancellationToken)
    {
        await ExecuteJobForSpecificCustomer("V_EXP_RETOUREN_To_storelogix_returns", "storelogix_kaufland", cancellationToken);
    }

    [TickerFunction(functionName: "RetourenSync_storelogix_transco", cronExpression: "%cronExpressions:normaleTabellen%")]
    public async Task ExecuteRetourenSyncJob_Transco(CancellationToken cancellationToken)
    {
        await ExecuteJobForSpecificCustomer("V_EXP_RETOUREN_To_storelogix_returns", "storelogix_transco", cancellationToken);
    }


    [TickerFunction(functionName: "RetourenSync_storelogix_zen", cronExpression: "%cronExpressions:normaleTabellen%")]
    public async Task ExecuteRetourenSyncJob_Zen(CancellationToken cancellationToken)
    {
        await ExecuteJobForSpecificCustomer("V_EXP_RETOUREN_To_storelogix_returns", "storelogix_zen", cancellationToken);
    }

    [TickerFunction(functionName: "RetourenSync_storelogix_ffs", cronExpression: "%cronExpressions:normaleTabellen%")]
    public async Task ExecuteRetourenSyncJob_Ffs(CancellationToken cancellationToken)
    {
        await ExecuteJobForSpecificCustomer("V_EXP_RETOUREN_To_storelogix_returns", "storelogix_ffs", cancellationToken);
    }

    [TickerFunction(functionName: "RetourenSync_storelogix_dfs", cronExpression: "%cronExpressions:normaleTabellen%")]
    public async Task ExecuteRetourenSyncJob_Dfs(CancellationToken cancellationToken)
    {
        await ExecuteJobForSpecificCustomer("V_EXP_RETOUREN_To_storelogix_returns", "storelogix_dfs", cancellationToken);
    }

    [TickerFunction(functionName: "RetourenSync_storelogix_schroeder", cronExpression: "%cronExpressions:normaleTabellen%")]
    public async Task ExecuteRetourenSyncJob_Schroeder(CancellationToken cancellationToken)
    {
        await ExecuteJobForSpecificCustomer("V_EXP_RETOUREN_To_storelogix_returns", "storelogix_schroeder", cancellationToken);
    }


    [TickerFunction(functionName: "RetourenSync_storelogix_petromax", cronExpression: "%cronExpressions:normaleTabellen%")]
    public async Task ExecuteRetourenSyncJob_Petromax(CancellationToken cancellationToken)
    {
        await ExecuteJobForSpecificCustomer("V_EXP_RETOUREN_To_storelogix_returns", "storelogix_petromax", cancellationToken);
    }



    [TickerFunction(functionName: "AuftragOrdersSync_storelogix_sendsally", cronExpression: "%cronExpressions:normaleTabellen%")]
    public async Task ExecuteAuftragOrdersSyncJob_SendSally(CancellationToken cancellationToken)
    {
        await ExecuteJobForSpecificCustomer("V_EXP_AUFTRAG_To_storelogix_orders", "storelogix_sendsally", cancellationToken);
    }

    [TickerFunction(functionName: "AuftragOrdersSync_storelogix_vertrieb2", cronExpression: "%cronExpressions:normaleTabellen%")]
    public async Task ExecuteAuftragOrdersSyncJob_Vertrieb2(CancellationToken cancellationToken)
    {
        await ExecuteJobForSpecificCustomer("V_EXP_AUFTRAG_To_storelogix_orders", "storelogix_vertrieb2", cancellationToken);
    }

    [TickerFunction(functionName: "AuftragOrdersSync_storelogix_cella", cronExpression: "%cronExpressions:normaleTabellen%")]
    public async Task ExecuteAuftragOrdersSyncJob_Cella(CancellationToken cancellationToken)
    {
        await ExecuteJobForSpecificCustomer("V_EXP_AUFTRAG_To_storelogix_orders", "storelogix_cella", cancellationToken);
    }

    [TickerFunction(functionName: "AuftragOrdersSync_storelogix_kaufland", cronExpression: "%cronExpressions:normaleTabellen%")]
    public async Task ExecuteAuftragOrdersSyncJob_Kaufland(CancellationToken cancellationToken)
    {
        await ExecuteJobForSpecificCustomer("V_EXP_AUFTRAG_To_storelogix_orders", "storelogix_kaufland", cancellationToken);
    }

    [TickerFunction(functionName: "AuftragOrdersSync_storelogix_transco", cronExpression: "%cronExpressions:normaleTabellen%")]
    public async Task ExecuteAuftragOrdersSyncJob_Transco(CancellationToken cancellationToken)
    {
        await ExecuteJobForSpecificCustomer("V_EXP_AUFTRAG_To_storelogix_orders", "storelogix_transco", cancellationToken);
    }


    [TickerFunction(functionName: "AuftragOrdersSync_storelogix_zen", cronExpression: "%cronExpressions:normaleTabellen%")]
    public async Task ExecuteAuftragOrdersSyncJob_Zen(CancellationToken cancellationToken)
    {
        await ExecuteJobForSpecificCustomer("V_EXP_AUFTRAG_To_storelogix_orders", "storelogix_zen", cancellationToken);
    }

    [TickerFunction(functionName: "AuftragOrdersSync_storelogix_ffs", cronExpression: "%cronExpressions:normaleTabellen%")]
    public async Task ExecuteAuftragOrdersSyncJob_Ffs(CancellationToken cancellationToken)
    {
        await ExecuteJobForSpecificCustomer("V_EXP_AUFTRAG_To_storelogix_orders", "storelogix_ffs", cancellationToken);
    }

    [TickerFunction(functionName: "AuftragOrdersSync_storelogix_dfs", cronExpression: "%cronExpressions:normaleTabellen%")]
    public async Task ExecuteAuftragOrdersSyncJob_Dfs(CancellationToken cancellationToken)
    {
        await ExecuteJobForSpecificCustomer("V_EXP_AUFTRAG_To_storelogix_orders", "storelogix_dfs", cancellationToken);
    }

    [TickerFunction(functionName: "AuftragOrdersSync_storelogix_schroeder", cronExpression: "%cronExpressions:normaleTabellen%")]
    public async Task ExecuteAuftragOrdersSyncJob_Schroeder(CancellationToken cancellationToken)
    {
        await ExecuteJobForSpecificCustomer("V_EXP_AUFTRAG_To_storelogix_orders", "storelogix_schroeder", cancellationToken);
    }


    [TickerFunction(functionName: "AuftragOrdersSync_storelogix_petromax", cronExpression: "%cronExpressions:normaleTabellen%")]
    public async Task ExecuteAuftragOrdersSyncJob_Petromax(CancellationToken cancellationToken)
    {
        await ExecuteJobForSpecificCustomer("V_EXP_AUFTRAG_To_storelogix_orders", "storelogix_petromax", cancellationToken);
    }



    [TickerFunction(functionName: "TaSync_storelogix_sendsally", cronExpression: "%cronExpressions:normaleTabellen%")]
    public async Task ExecuteTaSyncJob_SendSally(CancellationToken cancellationToken)
    {
        await ExecuteJobForSpecificCustomer("V_EXP_TA_To_storelogix_ta", "storelogix_sendsally", cancellationToken);
    }

    [TickerFunction(functionName: "TaSync_storelogix_vertrieb2", cronExpression: "%cronExpressions:normaleTabellen%")]
    public async Task ExecuteTaSyncJob_Vertrieb2(CancellationToken cancellationToken)
    {
        await ExecuteJobForSpecificCustomer("V_EXP_TA_To_storelogix_ta", "storelogix_vertrieb2", cancellationToken);
    }

    [TickerFunction(functionName: "TaSync_storelogix_cella", cronExpression: "%cronExpressions:normaleTabellen%")]
    public async Task ExecuteTaSyncJob_Cella(CancellationToken cancellationToken)
    {
        await ExecuteJobForSpecificCustomer("V_EXP_TA_To_storelogix_ta", "storelogix_cella", cancellationToken);
    }

    [TickerFunction(functionName: "TaSync_storelogix_kaufland", cronExpression: "%cronExpressions:normaleTabellen%")]
    public async Task ExecuteTaSyncJob_Kaufland(CancellationToken cancellationToken)
    {
        await ExecuteJobForSpecificCustomer("V_EXP_TA_To_storelogix_ta", "storelogix_kaufland", cancellationToken);
    }

    [TickerFunction(functionName: "TaSync_storelogix_transco", cronExpression: "%cronExpressions:normaleTabellen%")]
    public async Task ExecuteTaSyncJob_Transco(CancellationToken cancellationToken)
    {
        await ExecuteJobForSpecificCustomer("V_EXP_TA_To_storelogix_ta", "storelogix_transco", cancellationToken);
    }


    [TickerFunction(functionName: "TaSync_storelogix_zen", cronExpression: "%cronExpressions:normaleTabellen%")]
    public async Task ExecuteTaSyncJob_Zen(CancellationToken cancellationToken)
    {
        await ExecuteJobForSpecificCustomer("V_EXP_TA_To_storelogix_ta", "storelogix_zen", cancellationToken);
    }

    [TickerFunction(functionName: "TaSync_storelogix_ffs", cronExpression: "%cronExpressions:normaleTabellen%")]
    public async Task ExecuteTaSyncJob_Ffs(CancellationToken cancellationToken)
    {
        await ExecuteJobForSpecificCustomer("V_EXP_TA_To_storelogix_ta", "storelogix_ffs", cancellationToken);
    }

    [TickerFunction(functionName: "TaSync_storelogix_dfs", cronExpression: "%cronExpressions:normaleTabellen%")]
    public async Task ExecuteTaSyncJob_Dfs(CancellationToken cancellationToken)
    {
        await ExecuteJobForSpecificCustomer("V_EXP_TA_To_storelogix_ta", "storelogix_dfs", cancellationToken);
    }

    [TickerFunction(functionName: "TaSync_storelogix_schroeder", cronExpression: "%cronExpressions:normaleTabellen%")]
    public async Task ExecuteTaSyncJob_Schroeder(CancellationToken cancellationToken)
    {
        await ExecuteJobForSpecificCustomer("V_EXP_TA_To_storelogix_ta", "storelogix_schroeder", cancellationToken);
    }


    [TickerFunction(functionName: "TaSync_storelogix_petromax", cronExpression: "%cronExpressions:normaleTabellen%")]
    public async Task ExecuteTaSyncJob_Petromax(CancellationToken cancellationToken)
    {
        await ExecuteJobForSpecificCustomer("V_EXP_TA_To_storelogix_ta", "storelogix_petromax", cancellationToken);
    }



    [TickerFunction(functionName: "BestandNowSync_storelogix_sendsally", cronExpression: "%cronExpressions:normaleTabellen%")]
    public async Task ExecuteBestandNowSyncJob_SendSally(CancellationToken cancellationToken)
    {
        await ExecuteJobForSpecificCustomer("V_EXP_BESTAND_NOW_To_storelogix_stock_now", "storelogix_sendsally", cancellationToken);
    }

    [TickerFunction(functionName: "BestandNowSync_storelogix_vertrieb2", cronExpression: "%cronExpressions:normaleTabellen%")]
    public async Task ExecuteBestandNowSyncJob_Vertrieb2(CancellationToken cancellationToken)
    {
        await ExecuteJobForSpecificCustomer("V_EXP_BESTAND_NOW_To_storelogix_stock_now", "storelogix_vertrieb2", cancellationToken);
    }

    [TickerFunction(functionName: "BestandNowSync_storelogix_cella", cronExpression: "%cronExpressions:normaleTabellen%")]
    public async Task ExecuteBestandNowSyncJob_Cella(CancellationToken cancellationToken)
    {
        await ExecuteJobForSpecificCustomer("V_EXP_BESTAND_NOW_To_storelogix_stock_now", "storelogix_cella", cancellationToken);
    }

    [TickerFunction(functionName: "BestandNowSync_storelogix_kaufland", cronExpression: "%cronExpressions:normaleTabellen%")]
    public async Task ExecuteBestandNowSyncJob_Kaufland(CancellationToken cancellationToken)
    {
        await ExecuteJobForSpecificCustomer("V_EXP_BESTAND_NOW_To_storelogix_stock_now", "storelogix_kaufland", cancellationToken);
    }

    [TickerFunction(functionName: "BestandNowSync_storelogix_transco", cronExpression: "%cronExpressions:normaleTabellen%")]
    public async Task ExecuteBestandNowSyncJob_Transco(CancellationToken cancellationToken)
    {
        await ExecuteJobForSpecificCustomer("V_EXP_BESTAND_NOW_To_storelogix_stock_now", "storelogix_transco", cancellationToken);
    }


    [TickerFunction(functionName: "BestandNowSync_storelogix_zen", cronExpression: "%cronExpressions:normaleTabellen%")]
    public async Task ExecuteBestandNowSyncJob_Zen(CancellationToken cancellationToken)
    {
        await ExecuteJobForSpecificCustomer("V_EXP_BESTAND_NOW_To_storelogix_stock_now", "storelogix_zen", cancellationToken);
    }

    [TickerFunction(functionName: "BestandNowSync_storelogix_ffs", cronExpression: "%cronExpressions:normaleTabellen%")]
    public async Task ExecuteBestandNowSyncJob_Ffs(CancellationToken cancellationToken)
    {
        await ExecuteJobForSpecificCustomer("V_EXP_BESTAND_NOW_To_storelogix_stock_now", "storelogix_ffs", cancellationToken);
    }

    [TickerFunction(functionName: "BestandNowSync_storelogix_dfs", cronExpression: "%cronExpressions:normaleTabellen%")]
    public async Task ExecuteBestandNowSyncJob_Dfs(CancellationToken cancellationToken)
    {
        await ExecuteJobForSpecificCustomer("V_EXP_BESTAND_NOW_To_storelogix_stock_now", "storelogix_dfs", cancellationToken);
    }

    [TickerFunction(functionName: "BestandNowSync_storelogix_schroeder", cronExpression: "%cronExpressions:normaleTabellen%")]
    public async Task ExecuteBestandNowSyncJob_Schroeder(CancellationToken cancellationToken)
    {
        await ExecuteJobForSpecificCustomer("V_EXP_BESTAND_NOW_To_storelogix_stock_now", "storelogix_schroeder", cancellationToken);
    }


    [TickerFunction(functionName: "BestandNowSync_storelogix_petromax", cronExpression: "%cronExpressions:normaleTabellen%")]
    public async Task ExecuteBestandNowSyncJob_Petromax(CancellationToken cancellationToken)
    {
        await ExecuteJobForSpecificCustomer("V_EXP_BESTAND_NOW_To_storelogix_stock_now", "storelogix_petromax", cancellationToken);
    }



    [TickerFunction(functionName: "BestandSync_storelogix_sendsally", cronExpression: "%cronExpressions:taeglicheTabellen%")]
    public async Task ExecuteBestandSyncJob_SendSally(CancellationToken cancellationToken)
    {
        await ExecuteJobForSpecificCustomer("V_EXP_BESTAND_To_storelogix_stock", "storelogix_sendsally", cancellationToken);
    }

    [TickerFunction(functionName: "BestandSync_storelogix_vertrieb2", cronExpression: "%cronExpressions:taeglicheTabellen%")]
    public async Task ExecuteBestandSyncJob_Vertrieb2(CancellationToken cancellationToken)
    {
        await ExecuteJobForSpecificCustomer("V_EXP_BESTAND_To_storelogix_stock", "storelogix_vertrieb2", cancellationToken);
    }

    [TickerFunction(functionName: "BestandSync_storelogix_cella", cronExpression: "%cronExpressions:taeglicheTabellen%")]
    public async Task ExecuteBestandSyncJob_Cella(CancellationToken cancellationToken)
    {
        await ExecuteJobForSpecificCustomer("V_EXP_BESTAND_To_storelogix_stock", "storelogix_cella", cancellationToken);
    }

    [TickerFunction(functionName: "BestandSync_storelogix_kaufland", cronExpression: "%cronExpressions:taeglicheTabellen%")]
    public async Task ExecuteBestandSyncJob_Kaufland(CancellationToken cancellationToken)
    {
        await ExecuteJobForSpecificCustomer("V_EXP_BESTAND_To_storelogix_stock", "storelogix_kaufland", cancellationToken);
    }

    [TickerFunction(functionName: "BestandSync_storelogix_transco", cronExpression: "%cronExpressions:taeglicheTabellen%")]
    public async Task ExecuteBestandSyncJob_Transco(CancellationToken cancellationToken)
    {
        await ExecuteJobForSpecificCustomer("V_EXP_BESTAND_To_storelogix_stock", "storelogix_transco", cancellationToken);
    }


    [TickerFunction(functionName: "BestandSync_storelogix_zen", cronExpression: "%cronExpressions:taeglicheTabellen%")]
    public async Task ExecuteBestandSyncJob_Zen(CancellationToken cancellationToken)
    {
        await ExecuteJobForSpecificCustomer("V_EXP_BESTAND_To_storelogix_stock", "storelogix_zen", cancellationToken);
    }

    [TickerFunction(functionName: "BestandSync_storelogix_ffs", cronExpression: "%cronExpressions:taeglicheTabellen%")]
    public async Task ExecuteBestandSyncJob_Ffs(CancellationToken cancellationToken)
    {
        await ExecuteJobForSpecificCustomer("V_EXP_BESTAND_To_storelogix_stock", "storelogix_ffs", cancellationToken);
    }

    [TickerFunction(functionName: "BestandSync_storelogix_dfs", cronExpression: "%cronExpressions:taeglicheTabellen%")]
    public async Task ExecuteBestandSyncJob_Dfs(CancellationToken cancellationToken)
    {
        await ExecuteJobForSpecificCustomer("V_EXP_BESTAND_To_storelogix_stock", "storelogix_dfs", cancellationToken);
    }

    [TickerFunction(functionName: "BestandSync_storelogix_schroeder", cronExpression: "%cronExpressions:taeglicheTabellen%")]
    public async Task ExecuteBestandSyncJob_Schroeder(CancellationToken cancellationToken)
    {
        await ExecuteJobForSpecificCustomer("V_EXP_BESTAND_To_storelogix_stock", "storelogix_schroeder", cancellationToken);
    }


    [TickerFunction(functionName: "BestandSync_storelogix_petromax", cronExpression: "%cronExpressions:taeglicheTabellen%")]
    public async Task ExecuteBestandSyncJob_Petromax(CancellationToken cancellationToken)
    {
        await ExecuteJobForSpecificCustomer("V_EXP_BESTAND_To_storelogix_stock", "storelogix_petromax", cancellationToken);
    }



    [TickerFunction(functionName: "BestandMovesSync_storelogix_sendsally", cronExpression: "%cronExpressions:taeglicheTabellen%")]
    public async Task ExecuteBestandMovesSyncJob_SendSally(CancellationToken cancellationToken)
    {
        await ExecuteJobForSpecificCustomer("V_EXP_BESTAND_MOVES_To_storelogix_stock_moves", "storelogix_sendsally", cancellationToken);
    }

    [TickerFunction(functionName: "BestandMovesSync_storelogix_vertrieb2", cronExpression: "%cronExpressions:taeglicheTabellen%")]
    public async Task ExecuteBestandMovesSyncJob_Vertrieb2(CancellationToken cancellationToken)
    {
        await ExecuteJobForSpecificCustomer("V_EXP_BESTAND_MOVES_To_storelogix_stock_moves", "storelogix_vertrieb2", cancellationToken);
    }

    [TickerFunction(functionName: "BestandMovesSync_storelogix_cella", cronExpression: "%cronExpressions:taeglicheTabellen%")]
    public async Task ExecuteBestandMovesSyncJob_Cella(CancellationToken cancellationToken)
    {
        await ExecuteJobForSpecificCustomer("V_EXP_BESTAND_MOVES_To_storelogix_stock_moves", "storelogix_cella", cancellationToken);
    }

    [TickerFunction(functionName: "BestandMovesSync_storelogix_kaufland", cronExpression: "%cronExpressions:taeglicheTabellen%")]
    public async Task ExecuteBestandMovesSyncJob_Kaufland(CancellationToken cancellationToken)
    {
        await ExecuteJobForSpecificCustomer("V_EXP_BESTAND_MOVES_To_storelogix_stock_moves", "storelogix_kaufland", cancellationToken);
    }

    [TickerFunction(functionName: "BestandMovesSync_storelogix_transco", cronExpression: "%cronExpressions:taeglicheTabellen%")]
    public async Task ExecuteBestandMovesSyncJob_Transco(CancellationToken cancellationToken)
    {
        await ExecuteJobForSpecificCustomer("V_EXP_BESTAND_MOVES_To_storelogix_stock_moves", "storelogix_transco", cancellationToken);
    }


    [TickerFunction(functionName: "BestandMovesSync_storelogix_zen", cronExpression: "%cronExpressions:taeglicheTabellen%")]
    public async Task ExecuteBestandMovesSyncJob_Zen(CancellationToken cancellationToken)
    {
        await ExecuteJobForSpecificCustomer("V_EXP_BESTAND_MOVES_To_storelogix_stock_moves", "storelogix_zen", cancellationToken);
    }

    [TickerFunction(functionName: "BestandMovesSync_storelogix_ffs", cronExpression: "%cronExpressions:taeglicheTabellen%")]
    public async Task ExecuteBestandMovesSyncJob_Ffs(CancellationToken cancellationToken)
    {
        await ExecuteJobForSpecificCustomer("V_EXP_BESTAND_MOVES_To_storelogix_stock_moves", "storelogix_ffs", cancellationToken);
    }

    [TickerFunction(functionName: "BestandMovesSync_storelogix_dfs", cronExpression: "%cronExpressions:taeglicheTabellen%")]
    public async Task ExecuteBestandMovesSyncJob_Dfs(CancellationToken cancellationToken)
    {
        await ExecuteJobForSpecificCustomer("V_EXP_BESTAND_MOVES_To_storelogix_stock_moves", "storelogix_dfs", cancellationToken);
    }

    [TickerFunction(functionName: "BestandMovesSync_storelogix_schroeder", cronExpression: "%cronExpressions:taeglicheTabellen%")]
    public async Task ExecuteBestandMovesSyncJob_Schroeder(CancellationToken cancellationToken)
    {
        await ExecuteJobForSpecificCustomer("V_EXP_BESTAND_MOVES_To_storelogix_stock_moves", "storelogix_schroeder", cancellationToken);
    }


    [TickerFunction(functionName: "BestandMovesSync_storelogix_petromax", cronExpression: "%cronExpressions:taeglicheTabellen%")]
    public async Task ExecuteBestandMovesSyncJob_Petromax(CancellationToken cancellationToken)
    {
        await ExecuteJobForSpecificCustomer("V_EXP_BESTAND_MOVES_To_storelogix_stock_moves", "storelogix_petromax", cancellationToken);
    }



    public async Task ExecuteInitialImportForSpecificCustomer(string jobId, string customerId, CancellationToken cancellationToken)
    {
        var globalJobs = _configuration.GetSection("GlobalJobs").Get<List<GlobalJobConfig>>() ?? [];
        var customers = _configuration.GetSection("Customers").Get<List<CustomerConfig>>() ?? [];

        var customer = customers.FirstOrDefault(c => c.CustomerId == customerId);
        if (customer == null || !customer.IsEnabled)
        {
            _logger.LogInformation("Kunde {CustomerId} nicht gefunden oder deaktiviert", customerId);
            return;
        }

        var globalJob = globalJobs.FirstOrDefault(j => j.JobId == jobId);
        if (globalJob == null)
        {
            _logger.LogWarning("Job {JobId} für Kunde {CustomerId} nicht in Konfiguration gefunden", jobId, customerId);
            return;
        }

        var originalStrategy = globalJob.SyncStrategy;
        globalJob.SyncStrategy = "InitialImport";

        var uniqueJobId = $"{jobId}_{customerId}_MANUAL_INITIAL_{DateTime.UtcNow:yyyyMMdd_HHmmss}_{Guid.NewGuid():N}";

        try
        {
            _logger.LogInformation("Starte manuellen InitialImport für Job {JobId}, Kunde {CustomerId} mit eindeutiger ID {UniqueJobId}", jobId, customerId, uniqueJobId);

            var documentCount = await _importService.ExecuteJobAsync(customerId, globalJob, cancellationToken);

            _logger.LogInformation("Manueller InitialImport für Job {JobId}, Kunde {CustomerId} erfolgreich abgeschlossen. {DocumentCount} Dokumente importiert", jobId, customerId, documentCount);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Fehler beim manuellen InitialImport für Job {JobId}, Kunde {CustomerId}", jobId, customerId);
            throw;
        }
        finally
        {
            globalJob.SyncStrategy = originalStrategy;
        }
    }

    public async Task ExecuteJobForSpecificCustomer(string jobId, string customerId, CancellationToken cancellationToken)
    {
        var globalJobs = _configuration.GetSection("GlobalJobs").Get<List<GlobalJobConfig>>() ?? [];
        var customers = _configuration.GetSection("Customers").Get<List<CustomerConfig>>() ?? [];

        var customer = customers.FirstOrDefault(c => c.CustomerId == customerId);
        if (customer == null || !customer.IsEnabled)
        {
            _logger.LogInformation("Kunde {CustomerId} nicht gefunden oder deaktiviert", customerId);
            return;
        }

        var globalJob = globalJobs.FirstOrDefault(j => j.JobId == jobId);
        if (globalJob == null)
        {
            _logger.LogWarning("Job {JobId} für Kunde {CustomerId} nicht in Konfiguration gefunden", jobId, customerId);
            return;
        }

        if (!globalJob.IsEnabled)
        {
            _logger.LogInformation("Job {JobId} für Kunde {CustomerId} ist deaktiviert", jobId, customerId);
            return;
        }

        var uniqueJobId = $"{jobId}_{customerId}_{DateTime.UtcNow:yyyyMMdd_HHmmss}_{Guid.NewGuid():N}";

        try
        {
            _logger.LogInformation("Starte Job {JobId} für Kunde {CustomerId} mit eindeutiger ID {UniqueJobId}", jobId, customerId, uniqueJobId);

            var documentCount = await _importService.ExecuteJobAsync(customerId, globalJob, cancellationToken);

            _logger.LogInformation("Job {JobId} für Kunde {CustomerId} erfolgreich abgeschlossen", jobId, customerId);
        }
        catch (Exception ex)
        {
            var errorMessage = $"Fehler beim Ausführen von Job {jobId} für Kunde {customerId}: {ex.Message}";

            _logger.LogError(ex, "Fehler beim Ausführen von Job {JobId} für Kunde {CustomerId}", jobId, customerId);
            throw;
        }
    }
}
