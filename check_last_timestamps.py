#!/usr/bin/env python3


import pymongo
import os
from datetime import datetime
from typing import Optional, List, Tuple
from concurrent.futures import ThreadPoolExecutor, as_completed
import threading

def get_mongo_connection():
    connection_string = 'mongodb+srv://cs-admin:{password}@cs1.lwgp4.mongodb.net/?connectTimeoutMS=60000&maxIdleTimeMS=120000&socketTimeoutMS=120000&serverSelectionTimeoutMS=30000&heartbeatFrequencyMS=20000&maxPoolSize=50&minPoolSize=5&maxConnecting=5&waitQueueTimeoutMS=300000'

    connection_string = connection_string.replace('{password}', os.getenv('MONGODB_PASSWORD'))

    try:
        client = pymongo.MongoClient(connection_string)
        client.admin.command('ping')
        print(f"[INFO] Verbindung zu MongoDB erfolgreich: {connection_string.split('@')[-1] if '@' in connection_string else connection_string}")
        return client
    except Exception as e:
        print(f"[ERROR] <PERSON><PERSON> beim <PERSON> zu <PERSON>goDB: {e}")
        return None

def find_latest_update_in_collection(collection, field_name: str = 'LAST_UPDATE') -> Optional[Tuple[datetime, str]]:
    try:
        pipeline = [
            {"$match": {field_name: {"$exists": True, "$ne": None}}},
            {"$sort": {field_name: -1}},
            {"$limit": 1},
            {"$project": {field_name: 1}}
        ]

        result = list(collection.aggregate(pipeline))
        if not result:
            return None

        latest_doc = result[0]
        if field_name in latest_doc:
            timestamp = latest_doc[field_name]
            if isinstance(timestamp, datetime):
                return timestamp, collection.full_name
            elif hasattr(timestamp, 'as_datetime'):
                return timestamp.as_datetime(), collection.full_name

        return None
    except Exception as e:
        print(f"[WARNING] Fehler in Collection {collection.name}: {e}")
        return None

def process_collection(db, coll_name, field_name, print_lock):
    try:
        collection = db[coll_name]
        result = find_latest_update_in_collection(collection, field_name)

        with print_lock:
            if result:
                timestamp, full_name = result
                print(f"    [FOUND] {coll_name}: {timestamp}")
                return result
            else:
                print(f"    [SKIP] {coll_name}: kein {field_name} gefunden")

        return None
    except Exception as e:
        with print_lock:
            print(f"    [ERROR] {coll_name}: {e}")
        return None

def scan_all_databases(client, field_name: str = 'LAST_UPDATE') -> List[Tuple[datetime, str]]:
    all_timestamps = []

    all_db_names = client.list_database_names()
    db_names = [name for name in all_db_names
                if name.endswith('_test') and name not in ['admin', 'config', 'local']]

    print(f"\n[INFO] Durchsuche {len(db_names)} Test-Datenbank(en) nach '{field_name}' Feldern...")
    print(f"[INFO] Gefiltert aus {len(all_db_names)} Gesamtdatenbanken")

    for db_name in db_names:
        print(f"\n[DB] Datenbank: {db_name}")
        db = client[db_name]

        try:
            collection_names = db.list_collection_names()
            print(f"  [INFO] {len(collection_names)} Collection(s) gefunden")

            print_lock = threading.Lock()
            with ThreadPoolExecutor(max_workers=10) as executor:
                future_to_collection = {
                    executor.submit(process_collection, db, coll_name, field_name, print_lock): coll_name
                    for coll_name in collection_names
                }

                for future in as_completed(future_to_collection):
                    result = future.result()
                    if result:
                        all_timestamps.append(result)

        except Exception as e:
            print(f"  [ERROR] Fehler beim Zugriff auf Datenbank {db_name}: {e}")

    return all_timestamps

def main():
    print("MongoDB LAST_UPDATE Scanner (Test-Datenbanken)")
    print("=" * 60)

    client = get_mongo_connection()
    if not client:
        return

    try:
        all_timestamps = scan_all_databases(client)

        if not all_timestamps:
            print("\n[RESULT] Keine LAST_UPDATE Felder in den Test-Datenbanken gefunden!")
            return

        all_timestamps.sort(key=lambda x: x[0], reverse=True)

    except KeyboardInterrupt:
        print("\n\n[INFO] Abbruch durch Benutzer")
    except Exception as e:
        print(f"\n[ERROR] Unerwarteter Fehler: {e}")
    finally:
        if client:
            client.close()
            print("\n[INFO] MongoDB-Verbindung geschlossen")

if __name__ == "__main__":
    main()
