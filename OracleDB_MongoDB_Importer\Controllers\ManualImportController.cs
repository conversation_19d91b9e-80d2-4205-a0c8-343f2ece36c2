using Microsoft.AspNetCore.Mvc;
using OracleDB_MongoDB_Importer.Models;
using OracleDB_MongoDB_Importer.Services;

namespace OracleDB_MongoDB_Importer.Controllers;

[ApiController]
[Route("api/[controller]")]
public class ManualImportController : ControllerBase
{
    private readonly GlobalJobTickerService _globalJobTickerService;
    private readonly IConfiguration _configuration;
    private readonly ILogger<ManualImportController> _logger;

    public ManualImportController(
        GlobalJobTickerService globalJobTickerService,
        IConfiguration configuration,
        ILogger<ManualImportController> logger)
    {
        _globalJobTickerService = globalJobTickerService;
        _configuration = configuration;
        _logger = logger;
    }

    [HttpGet("initial-import-all")]
    public async Task<IActionResult> TriggerInitialImportForAllCustomersAndJobs(CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Starte Initial Import für ALLE Kunden und ALLE Jobs");

        var globalJobs = _configuration.GetSection("GlobalJobs").Get<List<GlobalJobConfig>>() ?? [];
        var customers = _configuration.GetSection("Customers").Get<List<CustomerConfig>>() ?? [];

        var results = new List<object>();
        var totalJobs = 0;
        var successfulJobs = 0;
        var failedJobs = 0;

        foreach (var customer in customers.Where(c => c.IsEnabled))
        {
            foreach (var job in globalJobs.Where(j => j.IsEnabled))
            {
                totalJobs++;
                try
                {
                    _logger.LogInformation("Starte Initial Import für Kunde {CustomerId} und Job {JobId}", customer.CustomerId, job.JobId);

                    await _globalJobTickerService.ExecuteInitialImportForSpecificCustomer(job.JobId, customer.CustomerId, cancellationToken);

                    successfulJobs++;
                    results.Add(new
                    {
                        customer = customer.CustomerId,
                        job = job.JobId,
                        status = "erfolgreich"
                    });

                    _logger.LogInformation("Initial Import erfolgreich für Kunde {CustomerId} und Job {JobId}", customer.CustomerId, job.JobId);
                }
                catch (Exception ex)
                {
                    failedJobs++;
                    _logger.LogError(ex, "Fehler beim Initial Import für Kunde {CustomerId} und Job {JobId}", customer.CustomerId, job.JobId);

                    results.Add(new
                    {
                        customer = customer.CustomerId,
                        job = job.JobId,
                        status = "fehler",
                        error = ex.Message
                    });
                }
            }
        }

        var summary = new
        {
            message = "Initial Import für alle Kunden und Jobs abgeschlossen",
            totalJobs,
            successfulJobs,
            failedJobs,
            results
        };

        _logger.LogInformation("Initial Import Zusammenfassung: {TotalJobs} Jobs gesamt, {SuccessfulJobs} erfolgreich, {FailedJobs} fehlgeschlagen",
            totalJobs, successfulJobs, failedJobs);

        return Ok(summary);
    }
}
