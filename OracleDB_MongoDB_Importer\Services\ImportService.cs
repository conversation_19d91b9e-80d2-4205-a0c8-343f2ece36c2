using OracleDB_MongoDB_Importer.Models;
using OracleDB_MongoDB_Importer.Repositories;
using OracleDB_MongoDB_Importer.Strategies;

namespace OracleDB_MongoDB_Importer.Services;

public interface IImportService
{
    Task<long> ExecuteJobAsync(string customerId, JobConfig jobConfig, CancellationToken cancellationToken);
}

public class ImportService : IImportService
{
    private readonly IImportStrategyFactory _strategyFactory;
    private readonly IMongoRepository _mongoRepository;
    private readonly JobMonitoringService _jobMonitoringService;
    private readonly ILogger<ImportService> _logger;

    public ImportService(IImportStrategyFactory strategyFactory, IMongoRepository mongoRepository, JobMonitoringService jobMonitoringService, ILogger<ImportService> logger)
    {
        _strategyFactory = strategyFactory;
        _mongoRepository = mongoRepository;
        _jobMonitoringService = jobMonitoringService;
        _logger = logger;
    }

    public async Task<long> ExecuteJobAsync(string customerId, JobConfig jobConfig, CancellationToken cancellationToken)
    {
        var jobmonitoring = Environment.GetEnvironmentVariable("JOB_MONITORING") ?? "false";
        if (!jobConfig.IsEnabled)
        {
            _logger.LogInformation("Job {JobId} ist deaktiviert", jobConfig.JobId);
            return 0;
        }
        if (jobmonitoring == "true") {
         await _jobMonitoringService.StartJobMonitoringAsync(jobConfig.JobId, customerId, jobConfig.OracleViewName, jobConfig.MongoCollectionName, cancellationToken);
        }
        _logger.LogInformation("Starte Job {JobId} mit Strategie {SyncStrategy}", jobConfig.JobId, jobConfig.SyncStrategy);

        try
        {
            string actualStrategy = jobConfig.SyncStrategy;

            if (jobConfig.SyncStrategy.ToLower() == "partialimport")
            {
                var lastTimestamp = await _mongoRepository.GetLastUpdateTimestampAsync(
                    customerId, jobConfig.MongoCollectionName, jobConfig.TimestampField ?? string.Empty, cancellationToken);

                if (lastTimestamp == null)
                {
                    actualStrategy = "InitialImport";
                }
            }

            var strategy = _strategyFactory.CreateStrategy(actualStrategy);
            var documentCount = await strategy.ExecuteAsync(customerId, jobConfig, cancellationToken);

            _logger.LogInformation("Job {JobId} erfolgreich abgeschlossen mit Strategie {ActualStrategy}, {DocumentCount} Dokumente verarbeitet", jobConfig.JobId, actualStrategy, documentCount);
            if (jobmonitoring == "true") {
             await _jobMonitoringService.StopJobMonitoringWithSuccessAsync(jobConfig.JobId, documentCount, cancellationToken);
            }
            return documentCount;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Fehler beim Ausführen von Job {JobId}", jobConfig.JobId);
            if (jobmonitoring == "true") {
             await _jobMonitoringService.StopJobMonitoringWithErrorAsync(jobConfig.JobId, ex.Message, cancellationToken);
            }
            throw;
        }
    }
}
