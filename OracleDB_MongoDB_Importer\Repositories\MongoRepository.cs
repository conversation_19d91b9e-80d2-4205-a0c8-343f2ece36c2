using MongoDB.Driver;
using MongoDB.Bson;
using OracleDB_MongoDB_Importer.Models;
using System.Data;
using System.Collections.Concurrent;

namespace OracleDB_MongoDB_Importer.Repositories;

public class MongoRepository : IMongoRepository, IDisposable
{
    private readonly IConfiguration _configuration;
    private readonly ILogger<MongoRepository> _logger;
    private static readonly ConcurrentDictionary<string, MongoClient> _clientCache = new();
    private static readonly ConcurrentDictionary<string, string> _connectionStringCache = new();
    private bool _disposed = false;

    public MongoRepository(IConfiguration configuration, ILogger<MongoRepository> logger)
    {
        _configuration = configuration;
        _logger = logger;
    }

    public async Task<bool> TestConnectionAsync(string customerId)
    {
        try
        {
            var database = await GetDatabaseAsync(customerId);
            await database.RunCommandAsync((Command<MongoDB.Bson.BsonDocument>)"{ping:1}");
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Fehler beim Testen der MongoDB-Verbindung für Kunde {CustomerId}", customerId);
            return false;
        }
    }

    public Task<IMongoDatabase> GetDatabaseAsync(string customerId)
    {
        var customer = GetCustomerConfig(customerId);
        var connectionString = GetCachedConnectionString(customer);

        var client = _clientCache.GetOrAdd(connectionString, cs =>
        {
            var settings = MongoClientSettings.FromConnectionString(cs);
            return new MongoClient(settings);
        });

        var database = client.GetDatabase(customer.MongoDbDatabaseName);
        return Task.FromResult(database);
    }

    private string GetCachedConnectionString(CustomerConfig customer)
    {
        return _connectionStringCache.GetOrAdd(customer.CustomerId, _ =>
            customer.MongoDbConnectionStringTemplate.Replace("{password}", GetMongoPassword()));
    }

    public async Task DeleteCollectionAsync(string customerId, string collectionName)
    {
        var database = await GetDatabaseAsync(customerId);
        await database.DropCollectionAsync(collectionName);
    }

    public async Task InsertDataBatchAsync(string customerId, string collectionName, DataTable data)
    {
        var database = await GetDatabaseAsync(customerId);
        var collection = database.GetCollection<BsonDocument>(collectionName);

        var documents = new List<BsonDocument>();

        foreach (DataRow row in data.Rows)
        {
            var document = new BsonDocument();
            foreach (DataColumn column in data.Columns)
            {
                var value = row[column];
                if (value != DBNull.Value)
                {
                    document[column.ColumnName] = BsonValue.Create(value);
                }
            }
            documents.Add(document);
        }

        if (documents.Count > 0)
        {
            await collection.InsertManyAsync(documents);
        }

        documents.Clear();
        documents.TrimExcess();
    }

    public async Task<long> GetDocumentCountAsync(string customerId, string collectionName)
    {
        var database = await GetDatabaseAsync(customerId);
        var collection = database.GetCollection<BsonDocument>(collectionName);

        var count = await collection.CountDocumentsAsync(FilterDefinition<BsonDocument>.Empty);

        return count;
    }

    public async Task EnsureIndexesForPartialImportAsync(
        string customerId,
        string collectionName,
        string timestampField,
        string? idField,
        CancellationToken cancellationToken = default)
    {
        if (string.IsNullOrWhiteSpace(collectionName))
        {
            throw new ArgumentException("collectionName darf nicht leer sein", nameof(collectionName));
        }

        if (string.IsNullOrWhiteSpace(timestampField))
        {
            throw new ArgumentException("timestampField darf nicht leer sein", nameof(timestampField));
        }

        var database = await GetDatabaseAsync(customerId);
        var collection = database.GetCollection<BsonDocument>(collectionName);

        using var existingCursor = await collection.Indexes.ListAsync(cancellationToken);
        var existingIndexes = await existingCursor.ToListAsync(cancellationToken);

        bool HasLeadingFieldIndex(string fieldName)
        {
            foreach (var idx in existingIndexes)
            {
                if (!idx.Contains("key") || !idx["key"].IsBsonDocument) continue;
                var keyDoc = idx["key"].AsBsonDocument;
                if (keyDoc.ElementCount == 0) continue;
                var firstElem = keyDoc.GetElement(0);
                if (firstElem.Name == fieldName) return true;
            }
            return false;
        }

        var indexModels = new List<CreateIndexModel<BsonDocument>>();

        if (!HasLeadingFieldIndex(timestampField))
        {
            var timestampIndexKeys = Builders<BsonDocument>.IndexKeys.Descending(timestampField);
            indexModels.Add(new CreateIndexModel<BsonDocument>(timestampIndexKeys, new CreateIndexOptions
            {
                Name = $"idx_{timestampField}_desc"
            }));
        }

        if (!string.IsNullOrWhiteSpace(idField) && !HasLeadingFieldIndex(idField))
        {
            var idIndexKeys = Builders<BsonDocument>.IndexKeys.Ascending(idField);
            indexModels.Add(new CreateIndexModel<BsonDocument>(idIndexKeys, new CreateIndexOptions
            {
                Name = $"idx_{idField}_asc",
                Unique = false
            }));
        }

        if (indexModels.Count == 0)
        {
            return;
        }

        try
        {
            await collection.Indexes.CreateManyAsync(indexModels, cancellationToken);
        }
        catch (MongoCommandException ex) when (ex.CodeName == "IndexOptionsConflict" || ex.CodeName == "IndexKeySpecsConflict")
        {
            _logger.LogWarning(ex, "Indexkonflikt bei EnsureIndexesForPartialImport für Collection {CollectionName}", collectionName);
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Fehler bei EnsureIndexesForPartialImport für Collection {CollectionName}", collectionName);
        }
    }

    public async Task InsertDocumentStreamAsync(string customerId, string collectionName, IAsyncEnumerable<BsonDocument> documentStream, int bufferSize = 1000, CancellationToken cancellationToken = default)
    {
        var database = await GetDatabaseAsync(customerId);
        var collection = database.GetCollection<BsonDocument>(collectionName);

        var buffer = new List<BsonDocument>(bufferSize);
        var totalInserted = 0;

        await foreach (var document in documentStream.WithCancellation(cancellationToken))
        {
            buffer.Add(document);

            if (buffer.Count >= bufferSize)
            {
                await collection.InsertManyAsync(buffer, cancellationToken: cancellationToken);
                totalInserted += buffer.Count;
                buffer.Clear();
                buffer.TrimExcess();

                await Task.Delay(250);
            }
        }

        if (buffer.Count > 0)
        {
            await collection.InsertManyAsync(buffer, cancellationToken: cancellationToken);
            totalInserted += buffer.Count;
            buffer.Clear();
            buffer.TrimExcess();
        }

        _logger.LogInformation("Stream-Import abgeschlossen: {TotalInserted} Dokumente in Collection {CollectionName}", totalInserted, collectionName);
    }

    public async Task DeleteDocumentsByIdsAsync(string customerId, string collectionName, string idField, IEnumerable<object> ids, CancellationToken cancellationToken = default)
    {
        var idList = ids.ToList();
        if (!idList.Any())
        {
            _logger.LogInformation("Keine IDs zum Löschen vorhanden");
            return;
        }

        var database = await GetDatabaseAsync(customerId);
        var collection = database.GetCollection<BsonDocument>(collectionName);

        const int batchSize = 1000;
        var totalDeleted = 0L;

        for (int i = 0; i < idList.Count; i += batchSize)
        {
            var batch = idList.Skip(i).Take(batchSize).ToList();
            var bsonIds = batch.Select(id => BsonValue.Create(id)).ToList();
            var filter = Builders<BsonDocument>.Filter.In(idField, bsonIds);

            var result = await collection.DeleteManyAsync(filter, cancellationToken);
            totalDeleted += result.DeletedCount;
        }
    }

    public async Task<DateTime?> GetLastUpdateTimestampAsync(string customerId, string collectionName, string timestampField, CancellationToken cancellationToken = default)
    {
        var retryCount = 0;
        const int maxRetries = 3;
        var retryDelay = TimeSpan.FromSeconds(2);

        while (retryCount <= maxRetries)
        {
            try
            {
                var database = await GetDatabaseAsync(customerId);
                var collection = database.GetCollection<BsonDocument>(collectionName);

                var sortOptions = new FindOptions<BsonDocument>
                {
                    Sort = Builders<BsonDocument>.Sort.Descending(timestampField),
                    Limit = 1,
                    Projection = Builders<BsonDocument>.Projection.Include(timestampField)
                };

                using var cancellationTokenSource = CancellationTokenSource.CreateLinkedTokenSource(cancellationToken);
                cancellationTokenSource.CancelAfter(TimeSpan.FromMinutes(5));

                var cursor = await collection.FindAsync(
                    FilterDefinition<BsonDocument>.Empty,
                    sortOptions,
                    cancellationTokenSource.Token);

                var latestDocument = await cursor.FirstOrDefaultAsync(cancellationTokenSource.Token);

                if (latestDocument?.Contains(timestampField) == true)
                {
                    var timestampValue = latestDocument[timestampField];
                    if (!timestampValue.IsBsonNull)
                    {
                        return timestampValue.ToUniversalTime();
                    }
                }
                return null;
            }
            catch (Exception ex) when ((ex is TimeoutException or MongoDB.Driver.MongoConnectionException or OperationCanceledException) && retryCount < maxRetries)
            {
                retryCount++;
                _logger.LogWarning("Operation bei GetLastUpdateTimestamp fehlgeschlagen, Versuch {RetryCount}/{MaxRetries}: {Message}", retryCount, maxRetries + 1, ex.Message);
                await Task.Delay(retryDelay * retryCount, cancellationToken);
            }
        }

        throw new TimeoutException($"GetLastUpdateTimestamp fehlgeschlagen nach {maxRetries + 1} Versuchen");
    }

    private CustomerConfig GetCustomerConfig(string customerId)
    {
        var customers = _configuration.GetSection("Customers").Get<CustomerConfig[]>() ?? [];
        var customer = customers.FirstOrDefault(c => c.CustomerId == customerId);

        if (customer == null)
        {
            throw new ArgumentException($"Kunde mit ID '{customerId}' nicht gefunden", nameof(customerId));
        }

        return customer;
    }

    private string GetMongoPassword()
    {
        var password = Environment.GetEnvironmentVariable("MONGODB_PASSWORD");

        return password ?? throw new InvalidOperationException($"MongoDB-Passwort nicht gefunden.");
    }



    public void Dispose()
    {
        Dispose(true);
        GC.SuppressFinalize(this);
    }

    protected virtual void Dispose(bool disposing)
    {
        if (!_disposed && disposing)
        {
            _disposed = true;
            DisposeAllClients();
        }
    }

    public static void DisposeAllClients()
    {
        foreach (var client in _clientCache.Values)
        {
            try
            {
                client.Cluster.Dispose();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Fehler beim Dispose von MongoClient: {ex.Message}");
            }
        }
        _clientCache.Clear();
        _connectionStringCache.Clear();
    }
}
