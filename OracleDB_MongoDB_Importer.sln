﻿
Microsoft Visual Studio Solution File, Format Version 12.00
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "OracleDB_MongoDB_Importer", "OracleDB_MongoDB_Importer\OracleDB_MongoDB_Importer.csproj", "{C1AC2884-D595-464B-B7ED-9CB31931F560}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{C1AC2884-D595-464B-B7ED-9CB31931F560}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{C1AC2884-D595-464B-B7ED-9CB31931F560}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{C1AC2884-D595-464B-B7ED-9CB31931F560}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{C1AC2884-D595-464B-B7ED-9CB31931F560}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
EndGlobal
