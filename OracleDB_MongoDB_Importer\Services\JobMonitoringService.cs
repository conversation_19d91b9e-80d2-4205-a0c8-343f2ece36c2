using OracleDB_MongoDB_Importer.Models;
using OracleDB_MongoDB_Importer.Repositories;

namespace OracleDB_MongoDB_Importer.Services;

public class JobMonitoringService
{
    private readonly IMonitoringRepository _monitoringRepository;
    private readonly Dictionary<string, JobMonitoringLog> _activeJobs = new();

    public JobMonitoringService(IMonitoringRepository monitoringRepository)
    {
        _monitoringRepository = monitoringRepository;
    }

    public async Task StartJobMonitoringAsync(string jobId, string customerId, string oracleViewName, string mongoCollectionName, CancellationToken cancellationToken = default)
    {
        var log = new JobMonitoringLog
        {
            JobId = jobId,
            CustomerId = customerId,
            OracleViewName = oracleViewName,
            MongoCollectionName = mongoCollectionName,
            StartTime = DateTime.UtcNow,
            EndTime = null,
            DurationMs = null,
            ErrorMessage = null,
            DocumentCount = null,
            CreatedAt = DateTime.UtcNow
        };

        await _monitoringRepository.SaveJobMonitoringLogAsync(log, cancellationToken);
        _activeJobs[jobId] = log;
    }

    public async Task StopJobMonitoringAsync(string jobId, long? documentCount = null, string? errorMessage = null, CancellationToken cancellationToken = default)
    {
        var endTime = DateTime.UtcNow;
        JobMonitoringLog? log = null;

        if (_activeJobs.TryGetValue(jobId, out var activeLog))
        {
            log = activeLog;
        }
        else
        {
            log = await _monitoringRepository.GetJobMonitoringLogAsync(jobId, cancellationToken);
        }

        if (log == null)
        {
            throw new InvalidOperationException($"Job monitoring log mit JobId '{jobId}' nicht gefunden");
        }

        var duration = (long)(endTime - log.StartTime).TotalMilliseconds;

        log.EndTime = endTime;
        log.DurationMs = duration;
        log.ErrorMessage = errorMessage;
        log.DocumentCount = documentCount;

        await _monitoringRepository.UpdateJobMonitoringLogAsync(log, cancellationToken);
        _activeJobs.Remove(jobId);
    }

    public async Task StopJobMonitoringWithErrorAsync(string jobId, string errorMessage, CancellationToken cancellationToken = default)
    {
        await StopJobMonitoringAsync(jobId, null, errorMessage, cancellationToken);
    }

    public async Task StopJobMonitoringWithSuccessAsync(string jobId, long documentCount, CancellationToken cancellationToken = default)
    {
        await StopJobMonitoringAsync(jobId, documentCount, null, cancellationToken);
    }

    public bool IsJobActive(string jobId)
    {
        return _activeJobs.ContainsKey(jobId);
    }

    public JobMonitoringLog? GetActiveJob(string jobId)
    {
        return _activeJobs.TryGetValue(jobId, out var log) ? log : null;
    }

    public IEnumerable<JobMonitoringLog> GetActiveJobs()
    {
        return _activeJobs.Values;
    }
}
