using MongoDB.Driver;
using MongoDB.Bson;
using System.Data;

namespace OracleDB_MongoDB_Importer.Repositories;

public interface IMongoRepository
{
    Task<bool> TestConnectionAsync(string customerId);
    Task<IMongoDatabase> GetDatabaseAsync(string customerId);
    Task DeleteCollectionAsync(string customerId, string collectionName);
    Task InsertDataBatchAsync(string customerId, string collectionName, DataTable data);
    Task InsertDocumentStreamAsync(string customerId, string collectionName, IAsyncEnumerable<BsonDocument> documentStream, int bufferSize = 1000, CancellationToken cancellationToken = default);
    Task<long> GetDocumentCountAsync(string customerId, string collectionName);
    Task DeleteDocumentsByIdsAsync(string customerId, string collectionName, string idField, IEnumerable<object> ids, CancellationToken cancellationToken = default);
    Task<DateTime?> GetLastUpdateTimestampAsync(string customerId, string collectionName, string timestampField,
        CancellationToken cancellationToken = default);
    Task EnsureIndexesForPartialImportAsync(
        string customerId,
        string collectionName,
        string timestampField,
        string? idField,
        CancellationToken cancellationToken = default);

}
