﻿FROM mcr.microsoft.com/dotnet/aspnet:9.0 AS base
USER $APP_UID
WORKDIR /app
EXPOSE 8080
EXPOSE 8081

FROM mcr.microsoft.com/dotnet/sdk:9.0 AS build
ARG BUILD_CONFIGURATION=Release
WORKDIR /src
COPY ["OracleDB_MongoDB_Importer/OracleDB_MongoDB_Importer.csproj", "OracleDB_MongoDB_Importer/"]
RUN dotnet restore "OracleDB_MongoDB_Importer/OracleDB_MongoDB_Importer.csproj"
COPY . .
WORKDIR "/src/OracleDB_MongoDB_Importer"
RUN dotnet build "./OracleDB_MongoDB_Importer.csproj" -c $BUILD_CONFIGURATION -o /app/build

FROM build AS publish
ARG BUILD_CONFIGURATION=Release
RUN dotnet publish "./OracleDB_MongoDB_Importer.csproj" -c $BUILD_CONFIGURATION -o /app/publish /p:UseAppHost=false

FROM base AS final
WORKDIR /app
COPY --from=publish /app/publish .
ENTRYPOINT ["dotnet", "OracleDB_MongoDB_Importer.dll"]
