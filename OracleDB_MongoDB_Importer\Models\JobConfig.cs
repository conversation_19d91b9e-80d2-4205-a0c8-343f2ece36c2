namespace OracleDB_MongoDB_Importer.Models;

public class JobConfig
{
    public string JobId { get; set; } = string.Empty;
    public string OracleViewName { get; set; } = string.Empty;
    public string MongoCollectionName { get; set; } = string.Empty;
    public string SyncStrategy { get; set; } = string.Empty;
    public string? TimestampField { get; set; }
    public string? IdField { get; set; }
    public int InitialImportBatchSize { get; set; } = 1000;
    public int SyncBatchSize { get; set; } = 500;
    public bool IsEnabled { get; set; } = true;
}

public class GlobalJobConfig : JobConfig
{
    public string CronExpressionKey { get; set; } = string.Empty;
}
