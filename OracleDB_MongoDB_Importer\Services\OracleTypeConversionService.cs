using Oracle.ManagedDataAccess.Client;
using Oracle.ManagedDataAccess.Types;
using MongoDB.Bson;
using System.Globalization;


namespace OracleDB_MongoDB_Importer.Services;

public class OracleTypeConversionService : IOracleTypeConversionService
{
    private readonly ILogger<OracleTypeConversionService> _logger;

    private static readonly Dictionary<string, Func<OracleDataReader, int, BsonValue>> TypeMappingToBson = new()
    {
        { "NUMBER", ConvertNumberToBson },
        { "DECIMAL", ConvertDecimalToBson },
        { "NUMERIC", ConvertDecimalToBson },
        { "FLOAT", ConvertFloatToBson },
        { "DOUBLE", ConvertDoubleToBson },
        { "INTEGER", ConvertIntegerToBson },
        { "INT", ConvertIntegerToBson },
        { "SMALLINT", ConvertSmallIntToBson },
        { "BIGINT", ConvertBigIntToBson },
        { "VARCHAR2", ConvertStringToBson },
        { "CHAR", ConvertStringToBson },
        { "NVARCHAR2", ConvertStringToBson },
        { "NCHAR", ConvertStringToBson },
        { "DATE", ConvertDateToBson },
        { "TIMESTAMP", ConvertTimestampToBson },
        { "CLOB", ConvertClobToBson },
        { "NCLOB", ConvertClobToBson }
    };

    private static readonly Dictionary<string, Func<OracleDataReader, int, object>> TypeMappingToObject = new()
    {
        { "NUMBER", ConvertNumberToObject },
        { "DECIMAL", ConvertDecimalToObject },
        { "NUMERIC", ConvertDecimalToObject },
        { "FLOAT", ConvertFloatToObject },
        { "DOUBLE", ConvertDoubleToObject },
        { "INTEGER", ConvertIntegerToObject },
        { "INT", ConvertIntegerToObject },
        { "SMALLINT", ConvertSmallIntToObject },
        { "BIGINT", ConvertBigIntToObject },
        { "VARCHAR2", ConvertStringToObject },
        { "CHAR", ConvertStringToObject },
        { "NVARCHAR2", ConvertStringToObject },
        { "NCHAR", ConvertStringToObject },
        { "DATE", ConvertDateToObject },
        { "TIMESTAMP", ConvertTimestampToObject },
        { "CLOB", ConvertClobToObject },
        { "NCLOB", ConvertClobToObject }
    };

    public OracleTypeConversionService(ILogger<OracleTypeConversionService> logger)
    {
        _logger = logger;
    }

    public BsonValue ConvertToSafeBsonValue(OracleDataReader reader, int index)
    {
        if (reader.IsDBNull(index))
            return BsonNull.Value;

        var dataType = reader.GetDataTypeName(index);

        if (TypeMappingToBson.TryGetValue(dataType.ToUpper(), out var converter))
        {
            try
            {
                return converter(reader, index);
            }
            catch (Exception ex)
            {
                var fieldName = reader.GetName(index);
                _logger.LogWarning("Konvertierung für Feld {FieldName} (Typ: {DataType}) fehlgeschlagen: {Error}", fieldName, dataType, ex.Message);
                return BsonNull.Value;
            }
        }

        try
        {
            return BsonValue.Create(reader.GetValue(index));
        }
        catch (Exception ex)
        {
            var fieldName = reader.GetName(index);
            _logger.LogWarning("Fallback-Konvertierung für Feld {FieldName} (Typ: {DataType}) fehlgeschlagen: {Error}", fieldName, dataType, ex.Message);
            return BsonNull.Value;
        }
    }

    public object ConvertToSafeObject(OracleDataReader reader, int index)
    {
        if (reader.IsDBNull(index))
            return DBNull.Value;

        var dataType = reader.GetDataTypeName(index);

        if (TypeMappingToObject.TryGetValue(dataType.ToUpper(), out var converter))
        {
            try
            {
                return converter(reader, index);
            }
            catch (Exception ex)
            {
                var fieldName = reader.GetName(index);
                _logger.LogWarning("Konvertierung für Feld {FieldName} (Typ: {DataType}) fehlgeschlagen: {Error}", fieldName, dataType, ex.Message);
                return DBNull.Value;
            }
        }

        try
        {
            return reader.GetValue(index);
        }
        catch (Exception ex)
        {
            var fieldName = reader.GetName(index);
            _logger.LogWarning("Fallback-Konvertierung für Feld {FieldName} (Typ: {DataType}) fehlgeschlagen: {Error}", fieldName, dataType, ex.Message);
            return DBNull.Value;
        }
    }

    private static BsonValue ConvertNumberToBson(OracleDataReader reader, int index)
        {
            var oracleDecimal = reader.GetOracleDecimal(index);
            if (oracleDecimal.IsNull)
                return BsonNull.Value;

        var precision = reader.GetSchemaTable()?.Rows[index]["NumericPrecision"];
        var scale = reader.GetSchemaTable()?.Rows[index]["NumericScale"];

        if (scale != null && Convert.ToInt32(scale) == 0 && precision != null)
        {
            var precisionValue = Convert.ToInt32(precision);
            if (precisionValue <= 10)
                return new BsonInt32((int)oracleDecimal.Value);
            if (precisionValue <= 19)
                return new BsonInt64((long)oracleDecimal.Value);
        }

        return new BsonDecimal128(new Decimal128(oracleDecimal.Value));
    }

    private static BsonValue ConvertDecimalToBson(OracleDataReader reader, int index)
    {
        var oracleDecimal = reader.GetOracleDecimal(index);
        return oracleDecimal.IsNull ? BsonNull.Value : new BsonDecimal128(new Decimal128(oracleDecimal.Value));
    }

    private static BsonValue ConvertFloatToBson(OracleDataReader reader, int index)
    {
        var value = reader.GetFloat(index);
        return new BsonDouble(value);
    }

    private static BsonValue ConvertDoubleToBson(OracleDataReader reader, int index)
    {
        var value = reader.GetDouble(index);
        return new BsonDouble(value);
    }

    private static BsonValue ConvertIntegerToBson(OracleDataReader reader, int index)
    {
        var value = reader.GetInt32(index);
        return new BsonInt32(value);
    }

    private static BsonValue ConvertSmallIntToBson(OracleDataReader reader, int index)
    {
        var value = reader.GetInt16(index);
        return new BsonInt32(value);
    }

    private static BsonValue ConvertBigIntToBson(OracleDataReader reader, int index)
    {
        var value = reader.GetInt64(index);
        return new BsonInt64(value);
    }

    private static BsonValue ConvertStringToBson(OracleDataReader reader, int index)
    {
        var value = reader.GetString(index);
        return new BsonString(value);
    }

    private static BsonValue ConvertDateToBson(OracleDataReader reader, int index)
    {
        var oracleDate = reader.GetOracleDate(index);
        if (oracleDate.IsNull)
                    return BsonNull.Value;

        return new BsonDateTime(oracleDate.Value);
    }

    private static BsonValue ConvertTimestampToBson(OracleDataReader reader, int index)
    {
        var oracleTimeStamp = reader.GetOracleTimeStamp(index);
        if (oracleTimeStamp.IsNull)
                    return BsonNull.Value;

        return new BsonDateTime(oracleTimeStamp.Value);
    }

    private static BsonValue ConvertClobToBson(OracleDataReader reader, int index)
    {
        var oracleClob = reader.GetOracleClob(index);
        if (oracleClob.IsNull)
                return BsonNull.Value;

        return new BsonString(oracleClob.Value);
    }

    private static object ConvertNumberToObject(OracleDataReader reader, int index)
        {
            var oracleDecimal = reader.GetOracleDecimal(index);
            if (oracleDecimal.IsNull)
                return DBNull.Value;

        var precision = reader.GetSchemaTable()?.Rows[index]["NumericPrecision"];
        var scale = reader.GetSchemaTable()?.Rows[index]["NumericScale"];

        if (scale != null && Convert.ToInt32(scale) == 0 && precision != null)
        {
            var precisionValue = Convert.ToInt32(precision);
            if (precisionValue <= 10)
                return (int)oracleDecimal.Value;
            if (precisionValue <= 19)
                return (long)oracleDecimal.Value;
        }

        return oracleDecimal.Value;
    }

    private static object ConvertDecimalToObject(OracleDataReader reader, int index)
    {
        var oracleDecimal = reader.GetOracleDecimal(index);
        return oracleDecimal.IsNull ? DBNull.Value : oracleDecimal.Value;
    }

    private static object ConvertFloatToObject(OracleDataReader reader, int index)
    {
        return reader.GetFloat(index);
    }

    private static object ConvertDoubleToObject(OracleDataReader reader, int index)
    {
        return reader.GetDouble(index);
    }

    private static object ConvertIntegerToObject(OracleDataReader reader, int index)
    {
        return reader.GetInt32(index);
    }

    private static object ConvertSmallIntToObject(OracleDataReader reader, int index)
    {
        return reader.GetInt16(index);
    }

    private static object ConvertBigIntToObject(OracleDataReader reader, int index)
    {
        return reader.GetInt64(index);
    }

    private static object ConvertStringToObject(OracleDataReader reader, int index)
    {
        return reader.GetString(index);
    }

    private static object ConvertDateToObject(OracleDataReader reader, int index)
    {
        var oracleDate = reader.GetOracleDate(index);
        return oracleDate.IsNull ? DBNull.Value : oracleDate.Value;
    }

    private static object ConvertTimestampToObject(OracleDataReader reader, int index)
    {
        var oracleTimeStamp = reader.GetOracleTimeStamp(index);
        return oracleTimeStamp.IsNull ? DBNull.Value : oracleTimeStamp.Value;
    }

    private static object ConvertClobToObject(OracleDataReader reader, int index)
    {
        var oracleClob = reader.GetOracleClob(index);
        return oracleClob.IsNull ? DBNull.Value : oracleClob.Value;
    }

}
